RÉSUMÉ COMPLET ET DÉTAILLÉ - SYSTÈME DE COMPTAGE BACCARAT
===========================================================

🎯 DÉFINITIONS FONDAMENTALES
----------------------------

MAIN vs MANCHE :
- MAIN : Toute distribution de cartes sur le tapis (incluant le brûlage)
- MANCHE : Comptée uniquement quand le résultat est Player ou Banker (pas TIE)

NUMÉROTATION :
- Main 0 : Brûlage (cas spécial, pas de manche)
- Main 1, 2, 3... : Mains de jeu normales
- Manche 0, 1, 2... : Compteur incrémenté seulement si résultat ≠ TIE

🔢 SYSTÈME D'INDEX COMPLET
---------------------------

INDEX 1 - <PERSON><PERSON><PERSON><PERSON> PAIR/IMPAIR DE LA MAIN :

Main 0 (Brûlage) :
- pair_2, pair_4, pair_6, pair_8, pair_10
- impair_3, impair_5, impair_7, impair_9, impair_11

Mains suivantes :
- pair_4 (4 cartes - aucune 3ème carte)
- pair_6 (6 cartes - deux 3èmes cartes)
- impair_5 (5 cartes - une 3ème carte)

INDEX 2 - SYNCHRONISATION :
- SYNC : Nombre total cumulé de cartes depuis le brûlage = PAIR
- DESYNC : Nombre total cumulé de cartes depuis le brûlage = IMPAIR

Règle absolue :
- Total cumulé PAIR → État SYNC
- Total cumulé IMPAIR → État DESYNC

Impact d'une main :
- Nombre IMPAIR de cartes dans la main → CHANGE l'état (sync↔desync OU desync↔sync)
- Nombre PAIR de cartes dans la main → CONSERVE l'état

INDEX 3 - COMBINÉ :
Combine Index 1 + Index 2 :
- pair_4_sync, pair_6_desync, impair_5_sync, etc.

INDEX 4 - RÉSULTAT :
- P (Player), B (Banker), T (TIE)

INDEX 5 - SAME/OPPOSITE :
Compare avec la manche précédente (ignore TIE) :
- S (Same) : Même résultat que la manche précédente
- O (Opposite) : Résultat différent de la manche précédente
- -- : Pas de changement (si TIE impliqué ou première manche)

🎲 LOGIQUE DE COMPTAGE
----------------------

Compteur de manches :
- Début : Compteur à 0
- TIE : Compteur reste identique
- Player ou Banker : Compteur s'incrémente de 1

Exemple :
Manche 0 → Main avec TIE → Reste Manche 0
Manche 0 → Main avec TIE → Reste Manche 0  
Manche 0 → Main avec Player → Devient Manche 1
Manche 1 → Main avec TIE → Reste Manche 1
Manche 1 → Main avec Banker → Devient Manche 2

État de synchronisation :
Brûlage: 3 cartes → Total: 3 (impair) → DESYNC initial
Main 1: 5 cartes → Total: 8 (pair) → SYNC (desync + impair = sync)
Main 2: 4 cartes → Total: 12 (pair) → SYNC (sync + pair = sync)
Main 3: 5 cartes → Total: 17 (impair) → DESYNC (sync + impair = desync)

Same/Opposite :
- Compare uniquement les résultats de manches (P ou B)
- Ignore complètement les TIE
- Première manche = -- (pas de comparaison possible)

⚠️ RÈGLES CLÉS
--------------

1. Main 0 = Brûlage (établit l'état initial sync/desync, pas de manche)
2. Manche incrémentée seulement si résultat ≠ TIE
3. État sync/desync déterminé par la parité du total cumulé depuis le brûlage
4. Same/Opposite compare uniquement les résultats de manches (ignore TIE)
5. Fin de partie dès que cut card atteinte
6. Chaque main produit tous les index pour un suivi complet
7. Le compteur de manche commence à 0 et ne s'incrémente que pour P ou B

📊 EXEMPLE COMPLET AVEC TOUS LES INDEX
---------------------------------------

Main | Cartes | Total | Parité | État  | Index1    | Index2 | Index3           | Index4 | Index5 | Compteur | N°Manche
-----|--------|-------|--------|-------|-----------|--------|------------------|--------|--------|----------|----------
0    | 3      | 3     | Impair | DESYNC| impair_3  | desync | impair_3_desync  | -      | -      | -        | -
1    | 5      | 8     | Pair   | SYNC  | impair_5  | sync   | impair_5_sync    | T      | --     | 0        | -
2    | 4      | 12    | Pair   | SYNC  | pair_4    | sync   | pair_4_sync      | P      | --     | 0        | 1
3    | 5      | 17    | Impair | DESYNC| impair_5  | desync | impair_5_desync  | B      | O      | 1        | 2
4    | 6      | 23    | Impair | DESYNC| pair_6    | desync | pair_6_desync    | T      | --     | 2        | -
5    | 4      | 27    | Impair | DESYNC| pair_4    | desync | pair_4_desync    | T      | --     | 2        | -
6    | 5      | 32    | Pair   | SYNC  | impair_5  | sync   | impair_5_sync    | B      | S      | 2        | 3
7    | 4      | 36    | Pair   | SYNC  | pair_4    | sync   | pair_4_sync      | T      | --     | 3        | -
8    | 6      | 42    | Pair   | SYNC  | pair_6    | sync   | pair_6_sync      | P      | O      | 3        | 4
9    | 5      | 47    | Impair | DESYNC| impair_5  | desync | impair_5_desync  | T      | --     | 4        | -
10   | 4      | 51    | Impair | DESYNC| pair_4    | desync | pair_4_desync    | P      | S      | 4        | 5

📋 EXPLICATION DES COLONNES
----------------------------

- Compteur affiché : Ce que montre le compteur de manches à l'écran
- N° Manche : Le numéro de la manche réelle (seulement quand P ou B)

Logique :
- TIE : Le compteur reste affiché mais aucune nouvelle manche n'est créée
- P ou B : Le compteur s'incrémente ET une nouvelle manche est enregistrée

🎯 SYNTHÈSE
-----------

Le système permet un tracking exhaustif de tous les patterns et états de la partie 
grâce aux 5 index qui capturent :
- La distribution des cartes (pair/impair)
- L'état de synchronisation global
- Les résultats de jeu
- Les patterns de répétition/alternance
- La progression des manches réelles

Chaque main génère simultanément tous ces index pour une analyse complète et en 
temps réel de la partie.

=== FIN DU SYSTÈME DE COMPTAGE ===
