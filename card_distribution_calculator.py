#!/usr/bin/env python3
"""
Calculateur de distribution des cartes dans un échantillon de 312 cartes
Base saine pour les calculs de Baccarat
"""

import math
from scipy.stats import hypergeom
import numpy as np

class CardDistributionCalculator:
    def __init__(self):
        # Configuration de base
        self.total_cards = 416  # 8 jeux de 52 cartes
        self.sample_size = 312  # Cartes utilisées avant cut card
        self.cards_per_value = 32  # 8 jeux × 4 couleurs = 32 cartes par valeur
        self.num_values = 13  # As, 2, 3, 4, 5, 6, 7, 8, 9, 10, J, Q, K
        
        # Valeurs des cartes pour le Baccarat
        self.card_values = {
            'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
            '10': 0, 'J': 0, 'Q': 0, 'K': 0
        }
        
        self.card_names = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
    
    def calculate_expected_distribution(self):
        """Calcule la distribution attendue de chaque valeur dans 312 cartes"""
        # Probabilité qu'une carte soit d'une valeur donnée
        prob_per_value = self.cards_per_value / self.total_cards  # 32/416 = 0.0769
        
        # Nombre attendu de cartes de chaque valeur dans 312 cartes
        expected_per_value = self.sample_size * prob_per_value  # 312 × (32/416) = 24
        
        print("=" * 60)
        print("DISTRIBUTION ATTENDUE DANS 312 CARTES")
        print("=" * 60)
        print(f"Total cartes: {self.total_cards}")
        print(f"Échantillon: {self.sample_size} cartes")
        print(f"Cartes par valeur initialement: {self.cards_per_value}")
        print(f"Probabilité par valeur: {prob_per_value:.4f}")
        print(f"Nombre attendu par valeur: {expected_per_value:.1f}")
        print()
        
        return expected_per_value
    
    def calculate_hypergeometric_distribution(self):
        """Calcule la distribution hypergeométrique pour chaque valeur"""
        print("=" * 60)
        print("DISTRIBUTION HYPERGEOMÉTRIQUE DÉTAILLÉE")
        print("=" * 60)
        
        results = {}
        
        for card_name in self.card_names:
            # Distribution hypergeométrique
            # N = population totale (416)
            # K = succès dans population (32 cartes de cette valeur)
            # n = échantillon (312)
            # k = succès dans échantillon (variable)
            
            rv = hypergeom(self.total_cards, self.cards_per_value, self.sample_size)
            
            # Statistiques
            mean = rv.mean()
            var = rv.var()
            std = rv.std()
            
            # Probabilités pour les valeurs les plus probables
            min_val = max(0, self.sample_size - (self.total_cards - self.cards_per_value))
            max_val = min(self.cards_per_value, self.sample_size)
            
            # Calcul des probabilités pour k = 20 à 28 (autour de la moyenne 24)
            probabilities = {}
            for k in range(max(min_val, 20), min(max_val + 1, 29)):
                prob = rv.pmf(k)
                probabilities[k] = prob
            
            results[card_name] = {
                'mean': mean,
                'variance': var,
                'std_dev': std,
                'min_possible': min_val,
                'max_possible': max_val,
                'probabilities': probabilities
            }
            
            print(f"{card_name:>2}: Moyenne = {mean:.2f}, Écart-type = {std:.2f}")
            print(f"     Plage possible: [{min_val}, {max_val}]")
            print(f"     Probabilités principales:")
            for k in sorted(probabilities.keys()):
                if probabilities[k] > 0.01:  # Afficher seulement si prob > 1%
                    print(f"       {k} cartes: {probabilities[k]:.4f} ({probabilities[k]*100:.2f}%)")
            print()
        
        return results
    
    def calculate_baccarat_value_distribution(self):
        """Calcule la distribution selon les valeurs Baccarat (0-9)"""
        print("=" * 60)
        print("DISTRIBUTION PAR VALEUR BACCARAT (0-9)")
        print("=" * 60)
        
        # Regroupement par valeur Baccarat
        baccarat_groups = {
            0: ['10', 'J', 'Q', 'K'],  # 4 types × 32 = 128 cartes
            1: ['A'],                   # 1 type × 32 = 32 cartes
            2: ['2'],                   # 1 type × 32 = 32 cartes
            3: ['3'],                   # 1 type × 32 = 32 cartes
            4: ['4'],                   # 1 type × 32 = 32 cartes
            5: ['5'],                   # 1 type × 32 = 32 cartes
            6: ['6'],                   # 1 type × 32 = 32 cartes
            7: ['7'],                   # 1 type × 32 = 32 cartes
            8: ['8'],                   # 1 type × 32 = 32 cartes
            9: ['9']                    # 1 type × 32 = 32 cartes
        }
        
        baccarat_distribution = {}
        
        for baccarat_value, card_types in baccarat_groups.items():
            total_cards_this_value = len(card_types) * self.cards_per_value
            
            # Distribution hypergeométrique pour cette valeur Baccarat
            rv = hypergeom(self.total_cards, total_cards_this_value, self.sample_size)
            
            mean = rv.mean()
            std = rv.std()
            
            baccarat_distribution[baccarat_value] = {
                'card_types': card_types,
                'total_cards': total_cards_this_value,
                'expected_in_312': mean,
                'std_dev': std,
                'probability': total_cards_this_value / self.total_cards
            }
            
            print(f"Valeur {baccarat_value}: {card_types}")
            print(f"  Total dans 416 cartes: {total_cards_this_value}")
            print(f"  Attendu dans 312 cartes: {mean:.2f} ± {std:.2f}")
            print(f"  Probabilité: {total_cards_this_value/self.total_cards:.4f}")
            print()
        
        return baccarat_distribution
    
    def run_complete_analysis(self):
        """Exécute l'analyse complète de distribution"""
        print("ANALYSE DE DISTRIBUTION DES CARTES")
        print("Base saine pour calculs Baccarat")
        print()
        
        # Calculs
        expected = self.calculate_expected_distribution()
        detailed = self.calculate_hypergeometric_distribution()
        baccarat_values = self.calculate_baccarat_value_distribution()
        
        print("=" * 60)
        print("RÉSUMÉ POUR LES CALCULS BACCARAT")
        print("=" * 60)
        print("Distribution la plus probable dans 312 cartes:")
        print("- Chaque valeur (A, 2, 3...K): ~24 cartes")
        print("- Valeur 0 (10,J,Q,K): ~96 cartes")
        print("- Valeurs 1-9: ~24 cartes chacune")
        print()
        print("Cette base peut maintenant être utilisée pour:")
        print("1. Calculer les probabilités de brûlage (2-11 cartes)")
        print("2. Simuler les règles de 3ème carte")
        print("3. Déterminer les parties purement paires/impaires")
        
        return {
            'expected_per_value': expected,
            'detailed_distribution': detailed,
            'baccarat_values': baccarat_values
        }

if __name__ == "__main__":
    try:
        calculator = CardDistributionCalculator()
        results = calculator.run_complete_analysis()
    except ImportError:
        print("Module scipy non disponible. Installation requise:")
        print("pip install scipy numpy")
        
        # Version simplifiée sans scipy
        print("\n" + "="*60)
        print("CALCUL SIMPLIFIÉ SANS SCIPY")
        print("="*60)
        
        total_cards = 416
        sample_size = 312
        cards_per_value = 32
        
        expected_per_value = sample_size * (cards_per_value / total_cards)
        
        print(f"Dans 312 cartes tirées de 416:")
        print(f"- Chaque valeur (A,2,3...K): ~{expected_per_value:.1f} cartes")
        print(f"- Valeur 0 (10,J,Q,K): ~{expected_per_value * 4:.1f} cartes")
        print(f"- Valeurs 1-9: ~{expected_per_value:.1f} cartes chacune")
