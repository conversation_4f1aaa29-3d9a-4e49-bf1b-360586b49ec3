#!/usr/bin/env python3
"""
Script pour lire le contenu d'un fichier PDF et l'afficher en texte.
"""

import PyPDF2
import sys
import os

def read_pdf(pdf_path):
    """
    Lit un fichier PDF et retourne son contenu textuel.
    
    Args:
        pdf_path (str): Chemin vers le fichier PDF
        
    Returns:
        str: Contenu textuel du PDF
    """
    try:
        # Vérifier si le fichier existe
        if not os.path.exists(pdf_path):
            return f"Erreur: Le fichier '{pdf_path}' n'existe pas."
        
        # Ouvrir le fichier PDF
        with open(pdf_path, 'rb') as file:
            # Créer un objet lecteur PDF
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Obtenir le nombre de pages
            num_pages = len(pdf_reader.pages)
            print(f"Le PDF contient {num_pages} page(s).\n")
            
            # Extraire le texte de toutes les pages
            full_text = ""
            for page_num in range(num_pages):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                full_text += f"\n--- PAGE {page_num + 1} ---\n"
                full_text += text
                full_text += "\n"
            
            return full_text
            
    except Exception as e:
        return f"Erreur lors de la lecture du PDF: {str(e)}"

def main():
    # Nom du fichier PDF à lire
    pdf_file = "punto-banco-rog-english.pdf"
    
    print(f"Lecture du fichier PDF: {pdf_file}")
    print("=" * 50)
    
    # Lire le contenu du PDF
    content = read_pdf(pdf_file)
    
    # Afficher le contenu
    print(content)
    
    # Optionnel: sauvegarder dans un fichier texte
    output_file = "pdf_content.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"\nContenu sauvegardé dans: {output_file}")
    except Exception as e:
        print(f"Erreur lors de la sauvegarde: {str(e)}")

if __name__ == "__main__":
    main()
