#!/usr/bin/env python3
"""
Calculateur exact du nombre de parties distinctes purement paires/impaires
Compte les parties possibles, pas les arrangements
"""

class BaccaratExactPartiesCount:
    def __init__(self):
        self.total_cards = 312
        
        print("Calculateur exact du nombre de parties distinctes")
        print(f"Total cartes: {self.total_cards}")
        print("Objectif: Compter les parties possibles (pas les arrangements)")
    
    def analyze_burn_configuration(self, burn_cards):
        """Analyse une configuration de brûlage pour compter les parties"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*60}")
        print(f"BRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'})")
        print(f"{'='*60}")
        print(f"Cartes restantes: {remaining_cards}")
        
        if burn_is_even:
            return self.count_even_parties(remaining_cards, burn_cards)
        else:
            return self.count_odd_parties(remaining_cards, burn_cards)
    
    def count_even_parties(self, remaining_cards, burn_cards):
        """Compte les parties purement paires possibles"""
        parties = {
            'type_1_pure_4': 0,
            'type_2_pure_6': 0,
            'type_3_mixed_4_6': 0
        }
        
        # TYPE 1: QUE des mains de 4 cartes
        max_hands_4 = remaining_cards // 4  # Troncature
        cards_used_4 = max_hands_4 * 4
        print(f"\nTYPE 1 - QUE des mains de 4 cartes:")
        print(f"  Maximum de mains: {max_hands_4}")
        print(f"  Cartes utilisées: {cards_used_4}")
        print(f"  Cartes non utilisées: {remaining_cards - cards_used_4}")
        
        if max_hands_4 > 0:
            parties['type_1_pure_4'] = 1
            print(f"  Parties possibles: 1")
        else:
            print(f"  Parties possibles: 0")
        
        # TYPE 2: QUE des mains de 6 cartes
        max_hands_6 = remaining_cards // 6  # Troncature
        cards_used_6 = max_hands_6 * 6
        print(f"\nTYPE 2 - QUE des mains de 6 cartes:")
        print(f"  Maximum de mains: {max_hands_6}")
        print(f"  Cartes utilisées: {cards_used_6}")
        print(f"  Cartes non utilisées: {remaining_cards - cards_used_6}")
        
        if max_hands_6 > 0:
            parties['type_2_pure_6'] = 1
            print(f"  Parties possibles: 1")
        else:
            print(f"  Parties possibles: 0")
        
        # TYPE 3: Mélanges de mains 4 et 6 cartes
        print(f"\nTYPE 3 - Mélanges de mains 4 et 6 cartes:")
        mixed_parties = self.count_mixed_4_6_parties(remaining_cards)
        parties['type_3_mixed_4_6'] = mixed_parties
        print(f"  Parties possibles: {mixed_parties}")
        
        total_even = parties['type_1_pure_4'] + parties['type_2_pure_6'] + parties['type_3_mixed_4_6']
        print(f"\nTOTAL parties purement paires pour brûlage {burn_cards}: {total_even}")
        
        return parties
    
    def count_odd_parties(self, remaining_cards, burn_cards):
        """Compte les parties purement impaires possibles"""
        parties = {
            'type_unique_pure_5': 0
        }
        
        # TYPE UNIQUE: QUE des mains de 5 cartes
        max_hands_5 = remaining_cards // 5  # Troncature
        cards_used_5 = max_hands_5 * 5
        print(f"\nTYPE UNIQUE - QUE des mains de 5 cartes:")
        print(f"  Maximum de mains: {max_hands_5}")
        print(f"  Cartes utilisées: {cards_used_5}")
        print(f"  Cartes non utilisées: {remaining_cards - cards_used_5}")
        
        if max_hands_5 > 0:
            parties['type_unique_pure_5'] = 1
            print(f"  Parties possibles: 1")
        else:
            print(f"  Parties possibles: 0")
        
        total_odd = parties['type_unique_pure_5']
        print(f"\nTOTAL parties purement impaires pour brûlage {burn_cards}: {total_odd}")
        
        return parties
    
    def count_mixed_4_6_parties(self, remaining_cards):
        """Compte les parties avec mélanges de mains 4 et 6 cartes"""
        mixed_count = 0
        valid_combinations = []
        
        # Énumérer toutes les combinaisons possibles
        max_hands_4 = remaining_cards // 4
        
        for hands_4 in range(0, max_hands_4 + 1):
            cards_after_4 = remaining_cards - (hands_4 * 4)
            max_hands_6 = cards_after_4 // 6
            
            for hands_6 in range(0, max_hands_6 + 1):
                total_hands = hands_4 + hands_6
                cards_used = (hands_4 * 4) + (hands_6 * 6)
                cards_unused = remaining_cards - cards_used
                
                # Vérifier si c'est une configuration valide
                if total_hands > 0 and cards_unused >= 0:
                    # Exclure les cas "purs" (que des 4 ou que des 6)
                    is_pure_4 = (hands_6 == 0 and hands_4 > 0)
                    is_pure_6 = (hands_4 == 0 and hands_6 > 0)
                    
                    if not is_pure_4 and not is_pure_6:
                        # C'est un vrai mélange
                        if hands_4 > 0 and hands_6 > 0:
                            mixed_count += 1
                            valid_combinations.append({
                                'hands_4': hands_4,
                                'hands_6': hands_6,
                                'total_hands': total_hands,
                                'cards_used': cards_used,
                                'cards_unused': cards_unused
                            })
        
        # Afficher quelques exemples
        print(f"  Combinaisons mixtes trouvées: {mixed_count}")
        for i, combo in enumerate(valid_combinations[:5]):  # Afficher les 5 premières
            print(f"    Partie {i+1}: {combo['hands_4']} mains de 4 + {combo['hands_6']} mains de 6")
            print(f"      Total mains: {combo['total_hands']}, Cartes non utilisées: {combo['cards_unused']}")
        
        if len(valid_combinations) > 5:
            print(f"    ... et {len(valid_combinations) - 5} autres combinaisons")
        
        return mixed_count
    
    def run_complete_count(self):
        """Exécute le comptage complet pour tous les brûlages"""
        
        print("="*70)
        print("CALCULATEUR EXACT DU NOMBRE DE PARTIES DISTINCTES")
        print("Comptage des parties possibles (pas des arrangements)")
        print("="*70)
        
        all_results = {}
        totals = {
            'pure_even_type_1': 0,  # QUE 4 cartes
            'pure_even_type_2': 0,  # QUE 6 cartes
            'pure_even_type_3': 0,  # Mélanges 4+6
            'pure_odd_type_unique': 0  # QUE 5 cartes
        }
        
        # Analyser chaque configuration de brûlage
        for burn_cards in range(2, 12):
            result = self.analyze_burn_configuration(burn_cards)
            all_results[burn_cards] = result
            
            # Additionner les totaux
            burn_is_even = (burn_cards % 2 == 0)
            if burn_is_even:
                totals['pure_even_type_1'] += result.get('type_1_pure_4', 0)
                totals['pure_even_type_2'] += result.get('type_2_pure_6', 0)
                totals['pure_even_type_3'] += result.get('type_3_mixed_4_6', 0)
            else:
                totals['pure_odd_type_unique'] += result.get('type_unique_pure_5', 0)
        
        print("\n" + "="*70)
        print("RÉSULTATS FINAUX - NOMBRE DE PARTIES DISTINCTES")
        print("="*70)
        print(f"1. Parties purement paires (QUE 4 cartes): {totals['pure_even_type_1']}")
        print(f"2. Parties purement paires (QUE 6 cartes): {totals['pure_even_type_2']}")
        print(f"3. Parties purement paires (mélanges 4+6): {totals['pure_even_type_3']}")
        print(f"4. Parties purement impaires (QUE 5 cartes): {totals['pure_odd_type_unique']}")
        
        total_pure_even = totals['pure_even_type_1'] + totals['pure_even_type_2'] + totals['pure_even_type_3']
        total_all = total_pure_even + totals['pure_odd_type_unique']
        
        print(f"\nTOTAL parties purement paires: {total_pure_even}")
        print(f"TOTAL parties purement impaires: {totals['pure_odd_type_unique']}")
        print(f"TOTAL GÉNÉRAL: {total_all}")
        
        print("\n" + "="*70)
        print("DÉTAIL PAR BRÛLAGE")
        print("="*70)
        for burn_cards in range(2, 12):
            result = all_results[burn_cards]
            burn_is_even = (burn_cards % 2 == 0)
            parity = "pair" if burn_is_even else "impair"
            
            print(f"\nBrûlage {burn_cards} ({parity}):")
            if burn_is_even:
                print(f"  QUE 4 cartes: {result.get('type_1_pure_4', 0)}")
                print(f"  QUE 6 cartes: {result.get('type_2_pure_6', 0)}")
                print(f"  Mélanges 4+6: {result.get('type_3_mixed_4_6', 0)}")
            else:
                print(f"  QUE 5 cartes: {result.get('type_unique_pure_5', 0)}")
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE")
        print("="*70)
        print("✓ Troncature à l'unité (pas d'arrondi)")
        print("✓ Comptage des parties distinctes possibles")
        print("✓ Séparation des types: QUE 4, QUE 6, QUE 5, mélanges 4+6")
        print("✓ Énumération exhaustive des combinaisons valides")
        print("✓ Exclusion des doublons (purs vs mélanges)")
        
        return {
            'totals': totals,
            'details': all_results
        }

if __name__ == "__main__":
    calculator = BaccaratExactPartiesCount()
    final_results = calculator.run_complete_count()
