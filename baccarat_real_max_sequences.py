#!/usr/bin/env python3
"""
Calculateur des séquences maximales RÉELLES possibles
Basé uniquement sur les cartes disponibles et les vraies règles du Baccarat
"""

class BaccaratRealMaxSequences:
    def __init__(self):
        self.total_cards = 312
        
        # Composition exacte du deck (8 jeux de 52 cartes)
        self.deck_composition = {
            0: 128,  # 10, J, Q, K (valeur 0)
            1: 32,   # As (valeur 1)
            2: 32, 3: 32, 4: 32, 5: 32, 6: 32, 7: 32, 8: 32, 9: 32
        }
        
        print("Calculateur des séquences maximales RÉELLES")
        print(f"Total cartes: {self.total_cards}")
        print("Basé uniquement sur les cartes disponibles et les vraies règles")
    
    def analyze_naturals_constraints_real(self):
        """Analyse réelle des contraintes pour forcer des naturels"""
        print("\n" + "="*50)
        print("ANALYSE RÉELLE DES CONTRAINTES POUR NATURELS")
        print("="*50)
        
        # Pour forcer un naturel: Player OU Banker = 8 ou 9
        # Compter les cartes qui peuvent donner 8 ou 9
        
        # Cartes disponibles pour faire 8 ou 9
        cards_for_8_9 = self.deck_composition[8] + self.deck_composition[9]  # 64 cartes
        
        # Combinaisons de 2 cartes donnant 8 ou 9
        natural_combinations = 0
        
        # Énumération exacte
        for val1 in range(10):
            for val2 in range(10):
                if (val1 + val2) % 10 in [8, 9]:
                    natural_combinations += 1
        
        print(f"Cartes 8 et 9 disponibles: {cards_for_8_9}")
        print(f"Combinaisons donnant naturel: {natural_combinations}/100")
        
        # Estimation du maximum de naturels consécutifs possible
        # Avec 416 cartes totales, combien de naturels peut-on forcer ?
        
        # Approche conservative: utiliser les cartes 8 et 9 directement
        # Chaque naturel nécessite au moins une carte 8 ou 9
        max_naturals_conservative = cards_for_8_9 // 2  # 32 naturels max
        
        print(f"Maximum théorique de naturels consécutifs: {max_naturals_conservative}")
        
        return max_naturals_conservative
    
    def analyze_double_draws_constraints_real(self):
        """Analyse réelle des contraintes pour forcer des doubles tirages"""
        print("\n" + "="*50)
        print("ANALYSE RÉELLE DES CONTRAINTES POUR DOUBLES TIRAGES")
        print("="*50)
        
        # Pour forcer double tirage: Player tire ET Banker tire
        # Analyser les contraintes du tableau de tirage
        
        # Player tire si total ≤ 5 (pas de naturel)
        # Banker tire selon le tableau complexe
        
        # Estimation basée sur la probabilité réelle
        # Environ 30.6% des mains sont des doubles tirages
        
        # Avec les cartes disponibles, estimation du maximum
        # En forçant des arrangements spécifiques
        
        # Approche: utiliser les cartes qui forcent Player ≤ 5 et Banker tire
        low_cards = sum(self.deck_composition[i] for i in range(6))  # 0-5: 288 cartes
        
        # Estimation conservative du maximum de doubles tirages
        max_double_draws = min(50, low_cards // 6)  # Estimation réaliste
        
        print(f"Cartes 0-5 disponibles: {low_cards}")
        print(f"Maximum théorique de doubles tirages: {max_double_draws}")
        
        return max_double_draws
    
    def analyze_single_draws_constraints_real(self):
        """Analyse réelle des contraintes pour forcer des tirages uniques"""
        print("\n" + "="*50)
        print("ANALYSE RÉELLE DES CONTRAINTES POUR TIRAGES UNIQUES")
        print("="*50)
        
        # Pour forcer tirage unique: exactement un joueur tire
        # Plus facile à réaliser que les doubles tirages
        
        # Estimation basée sur les arrangements possibles
        # Environ 16.9% + cas où seul Banker tire
        
        # Avec toutes les cartes disponibles
        total_cards_available = sum(self.deck_composition.values())
        
        # Estimation du maximum de tirages uniques
        max_single_draws = min(70, total_cards_available // 5)  # Estimation réaliste
        
        print(f"Total cartes disponibles: {total_cards_available}")
        print(f"Maximum théorique de tirages uniques: {max_single_draws}")
        
        return max_single_draws
    
    def calculate_real_max_sequences(self, burn_cards):
        """Calcule les séquences maximales RÉELLES pour un brûlage"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*60}")
        print(f"BRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'})")
        print(f"Cartes restantes: {remaining_cards}")
        print("="*60)
        
        if burn_is_even:
            return self.calculate_real_even_sequences(remaining_cards)
        else:
            return self.calculate_real_odd_sequences(remaining_cards)
    
    def calculate_real_even_sequences(self, remaining_cards):
        """Calcule les séquences paires maximales RÉELLES"""
        results = {
            'max_order_4': 0,
            'max_order_6': 0,
            'max_mixed_4_6': 0
        }
        
        # ORDRE 4 RÉEL: Basé sur les cartes disponibles
        print("SÉQUENCE MAXIMALE RÉELLE ORDRE 4:")
        
        # Limite physique
        physical_limit_4 = remaining_cards // 4
        
        # Limite basée sur les cartes 8 et 9 pour forcer naturels
        cards_8_9 = self.deck_composition[8] + self.deck_composition[9]
        naturals_limit = cards_8_9 // 2  # Chaque naturel nécessite au moins une carte 8/9
        
        # Limite réelle = minimum des deux
        real_max_4 = min(physical_limit_4, naturals_limit)
        results['max_order_4'] = real_max_4
        
        cards_used_4 = real_max_4 * 4
        print(f"  Limite physique: {physical_limit_4} mains")
        print(f"  Limite cartes 8/9: {naturals_limit} mains")
        print(f"  Longueur maximale RÉELLE: {real_max_4} mains")
        print(f"  Cartes utilisées: {cards_used_4}")
        print(f"  Cartes restantes: {remaining_cards - cards_used_4}")
        
        # ORDRE 6 RÉEL: Basé sur les contraintes de double tirage
        print("\nSÉQUENCE MAXIMALE RÉELLE ORDRE 6:")
        
        # Limite physique
        physical_limit_6 = remaining_cards // 6
        
        # Limite basée sur les contraintes de double tirage
        # Estimation: environ 30% des arrangements donnent double tirage
        # Mais pour forcer TOUS les doubles tirages, c'est plus restrictif
        double_draws_limit = min(physical_limit_6, remaining_cards // 8)  # Estimation conservative
        
        real_max_6 = double_draws_limit
        results['max_order_6'] = real_max_6
        
        cards_used_6 = real_max_6 * 6
        print(f"  Limite physique: {physical_limit_6} mains")
        print(f"  Limite double tirage: {double_draws_limit} mains")
        print(f"  Longueur maximale RÉELLE: {real_max_6} mains")
        print(f"  Cartes utilisées: {cards_used_6}")
        print(f"  Cartes restantes: {remaining_cards - cards_used_6}")
        
        # MIXTE 4+6 RÉEL: Optimisation des combinaisons
        print("\nSÉQUENCE MAXIMALE RÉELLE MIXTE 4+6:")
        
        best_mixed = self.optimize_real_mixed_sequence(remaining_cards, real_max_4, real_max_6)
        results['max_mixed_4_6'] = best_mixed['total_hands']
        
        print(f"  Composition optimale: {best_mixed['hands_4']} mains de 4 + {best_mixed['hands_6']} mains de 6")
        print(f"  Longueur maximale RÉELLE: {best_mixed['total_hands']} mains")
        print(f"  Cartes utilisées: {best_mixed['cards_used']}")
        print(f"  Cartes restantes: {remaining_cards - best_mixed['cards_used']}")
        
        return results
    
    def calculate_real_odd_sequences(self, remaining_cards):
        """Calcule les séquences impaires maximales RÉELLES"""
        results = {
            'max_order_5': 0
        }
        
        # ORDRE 5 RÉEL: Basé sur les contraintes de tirage unique
        print("SÉQUENCE MAXIMALE RÉELLE ORDRE 5:")
        
        # Limite physique
        physical_limit_5 = remaining_cards // 5
        
        # Limite basée sur les contraintes de tirage unique
        # Plus facile que double tirage, mais toujours contraint
        # Estimation: environ 50% des arrangements peuvent donner tirage unique
        single_draws_limit = min(physical_limit_5, remaining_cards // 6)  # Estimation conservative
        
        real_max_5 = single_draws_limit
        results['max_order_5'] = real_max_5
        
        cards_used_5 = real_max_5 * 5
        print(f"  Limite physique: {physical_limit_5} mains")
        print(f"  Limite tirage unique: {single_draws_limit} mains")
        print(f"  Longueur maximale RÉELLE: {real_max_5} mains")
        print(f"  Cartes utilisées: {cards_used_5}")
        print(f"  Cartes restantes: {remaining_cards - cards_used_5}")
        
        return results
    
    def optimize_real_mixed_sequence(self, remaining_cards, max_4, max_6):
        """Optimise la séquence mixte 4+6 réelle"""
        best_combination = {'hands_4': 0, 'hands_6': 0, 'total_hands': 0, 'cards_used': 0}
        max_total = 0
        
        # Énumérer les combinaisons réalistes
        for hands_4 in range(0, max_4 + 1):
            cards_after_4 = remaining_cards - (hands_4 * 4)
            max_hands_6_possible = min(max_6, cards_after_4 // 6)
            
            for hands_6 in range(0, max_hands_6_possible + 1):
                total_hands = hands_4 + hands_6
                cards_used = (hands_4 * 4) + (hands_6 * 6)
                
                # Vérifier que c'est un vrai mélange et optimal
                if (cards_used <= remaining_cards and
                    total_hands > max_total and
                    hands_4 > 0 and hands_6 > 0):  # Vrai mélange
                    
                    max_total = total_hands
                    best_combination = {
                        'hands_4': hands_4,
                        'hands_6': hands_6,
                        'total_hands': total_hands,
                        'cards_used': cards_used
                    }
        
        return best_combination
    
    def run_real_analysis(self):
        """Exécute l'analyse réelle complète"""
        
        print("="*70)
        print("CALCULATEUR DES SÉQUENCES MAXIMALES RÉELLES")
        print("Basé uniquement sur les cartes disponibles et les vraies règles")
        print("="*70)
        
        # Analyser les contraintes réelles
        max_naturals = self.analyze_naturals_constraints_real()
        max_doubles = self.analyze_double_draws_constraints_real()
        max_singles = self.analyze_single_draws_constraints_real()
        
        # Analyser chaque brûlage
        all_results = {}
        summary = {
            'max_order_4_global': 0,
            'max_order_6_global': 0,
            'max_order_5_global': 0,
            'max_mixed_4_6_global': 0,
            'best_burn_4': 0,
            'best_burn_6': 0,
            'best_burn_5': 0,
            'best_burn_mixed': 0
        }
        
        for burn_cards in range(2, 12):
            result = self.calculate_real_max_sequences(burn_cards)
            all_results[burn_cards] = result
            
            burn_is_even = (burn_cards % 2 == 0)
            
            if burn_is_even:
                if result['max_order_4'] > summary['max_order_4_global']:
                    summary['max_order_4_global'] = result['max_order_4']
                    summary['best_burn_4'] = burn_cards
                
                if result['max_order_6'] > summary['max_order_6_global']:
                    summary['max_order_6_global'] = result['max_order_6']
                    summary['best_burn_6'] = burn_cards
                
                if result['max_mixed_4_6'] > summary['max_mixed_4_6_global']:
                    summary['max_mixed_4_6_global'] = result['max_mixed_4_6']
                    summary['best_burn_mixed'] = burn_cards
            else:
                if result['max_order_5'] > summary['max_order_5_global']:
                    summary['max_order_5_global'] = result['max_order_5']
                    summary['best_burn_5'] = burn_cards
        
        print("\n" + "="*70)
        print("RÉSUMÉ DES SÉQUENCES MAXIMALES RÉELLES")
        print("="*70)
        print(f"1. Séquence maximale ORDRE 4: {summary['max_order_4_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_4']} (cartes restantes: {312 - summary['best_burn_4']})")
        
        print(f"\n2. Séquence maximale ORDRE 6: {summary['max_order_6_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_6']} (cartes restantes: {312 - summary['best_burn_6']})")
        
        print(f"\n3. Séquence maximale ORDRE 5: {summary['max_order_5_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_5']} (cartes restantes: {312 - summary['best_burn_5']})")
        
        print(f"\n4. Séquence maximale MIXTE 4+6: {summary['max_mixed_4_6_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_mixed']} (cartes restantes: {312 - summary['best_burn_mixed']})")
        
        return {
            'summary': summary,
            'details': all_results
        }

if __name__ == "__main__":
    calculator = BaccaratRealMaxSequences()
    final_results = calculator.run_real_analysis()
