#!/usr/bin/env python3
"""
Calculateur mathématique pur pour parties purement paires/impaires au Baccarat
Basé sur les équations exactes des contraintes de tirage
"""

from math import comb, factorial
from fractions import Fraction

class BaccaratPureMathematicalCalculator:
    def __init__(self):
        # Configuration exacte
        self.total_cards = 312
        
        # Composition exacte du deck (8 jeux de 52 cartes)
        self.deck_composition = {
            0: 128,  # 10, J, Q, K (32 × 4 = 128)
            1: 32,   # As
            2: 32,   # 2
            3: 32,   # 3
            4: 32,   # 4
            5: 32,   # 5
            6: 32,   # 6
            7: 32,   # 7
            8: 32,   # 8
            9: 32    # 9
        }
        
        # Probabilités exactes selon Wizard of Odds (8 decks)
        self.exact_probabilities = {
            4: Fraction(2292252566437888, ****************),  # Naturels + aucune 3ème carte
            5: Fraction(2230518282592256, ****************),  # Un seul joueur tire
            6: Fraction(***************, ****************)    # Les deux joueurs tirent
        }
        
        print("Calculateur mathématique pur - Baccarat")
        print(f"Total cartes: {self.total_cards}")
        print("Équations exactes basées sur les contraintes de tirage")
        
        # Afficher les probabilités exactes
        print(f"\nProbabilités exactes (8 decks):")
        for cards, prob in self.exact_probabilities.items():
            print(f"  {cards} cartes: {float(prob):.6f}")
    
    def calculate_natural_constraints(self, num_hands):
        """Calcule les contraintes exactes pour forcer des naturels (4 cartes)"""
        # Pour avoir un naturel: Player OU Banker doit avoir 8 ou 9
        # Contraintes sur les 2 premières cartes
        
        # Probabilité qu'une main soit un naturel
        prob_natural = self.exact_probabilities[4]
        
        # Nombre de façons d'obtenir un naturel avec 2 cartes
        # Total = 8 ou 9 (modulo 10)
        natural_combinations = 0
        
        # Énumération des combinaisons qui donnent 8 ou 9
        for val1 in range(10):
            for val2 in range(10):
                total = (val1 + val2) % 10
                if total in [8, 9]:
                    # Compter les cartes disponibles pour chaque valeur
                    count1 = self.deck_composition[val1]
                    count2 = self.deck_composition[val2]
                    
                    if val1 == val2:
                        # Même valeur: C(count, 2)
                        natural_combinations += comb(count1, 2)
                    else:
                        # Valeurs différentes: count1 × count2
                        natural_combinations += count1 * count2
        
        # Total de combinaisons possibles pour 2 cartes
        total_combinations = comb(416, 2)
        
        # Probabilité exacte d'un naturel
        exact_natural_prob = Fraction(natural_combinations, total_combinations)
        
        # Nombre d'arrangements pour forcer num_hands naturels consécutifs
        # C(deck, cartes_nécessaires) × (prob_natural)^num_hands
        cards_needed = num_hands * 4
        
        if cards_needed <= 416:
            base_arrangements = comb(416, cards_needed)
            constraint_factor = exact_natural_prob ** num_hands
            
            return int(base_arrangements * constraint_factor)
        
        return 0
    
    def calculate_single_draw_constraints(self, num_hands):
        """Calcule les contraintes exactes pour forcer des tirages uniques (5 cartes)"""
        # Pour avoir 5 cartes: exactement un joueur tire
        
        prob_single_draw = self.exact_probabilities[5]
        cards_needed = num_hands * 5
        
        if cards_needed <= 416:
            base_arrangements = comb(416, cards_needed)
            constraint_factor = prob_single_draw ** num_hands
            
            return int(base_arrangements * constraint_factor)
        
        return 0
    
    def calculate_double_draw_constraints(self, num_hands):
        """Calcule les contraintes exactes pour forcer des doubles tirages (6 cartes)"""
        # Pour avoir 6 cartes: les deux joueurs tirent
        
        prob_double_draw = self.exact_probabilities[6]
        cards_needed = num_hands * 6
        
        if cards_needed <= 416:
            base_arrangements = comb(416, cards_needed)
            constraint_factor = prob_double_draw ** num_hands
            
            return int(base_arrangements * constraint_factor)
        
        return 0
    
    def calculate_mixed_constraints(self, hands_4, hands_6):
        """Calcule les contraintes pour un mélange de mains 4 et 6 cartes"""
        total_hands = hands_4 + hands_6
        cards_needed = (hands_4 * 4) + (hands_6 * 6)
        
        if cards_needed <= 416:
            # Coefficient multinomial pour l'ordre des mains
            order_factor = factorial(total_hands) // (factorial(hands_4) * factorial(hands_6))
            
            # Contraintes de probabilité
            prob_4 = self.exact_probabilities[4]
            prob_6 = self.exact_probabilities[6]
            
            base_arrangements = comb(416, cards_needed)
            constraint_factor = (prob_4 ** hands_4) * (prob_6 ** hands_6)
            
            return int(base_arrangements * constraint_factor * order_factor)
        
        return 0
    
    def analyze_burn_configuration_exact(self, burn_cards):
        """Analyse exacte d'une configuration de brûlage"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*60}")
        print(f"ANALYSE MATHÉMATIQUE EXACTE - BRÛLAGE {burn_cards}")
        print(f"{'='*60}")
        print(f"Cartes restantes: {remaining_cards}")
        print(f"Parité: {'PAIR' if burn_is_even else 'IMPAIR'}")
        
        results = {
            'burn_cards': burn_cards,
            'remaining_cards': remaining_cards,
            'burn_is_even': burn_is_even,
            'arrangements': {}
        }
        
        if burn_is_even:
            # Configurations paires
            results['arrangements'] = self.calculate_even_arrangements_exact(remaining_cards)
        else:
            # Configurations impaires
            results['arrangements'] = self.calculate_odd_arrangements_exact(remaining_cards)
        
        return results
    
    def calculate_even_arrangements_exact(self, remaining_cards):
        """Calcule les arrangements exacts pour brûlage pair"""
        arrangements = {
            'pure_4_only': 0,
            'pure_6_only': 0,
            'mixed_4_and_6': 0
        }
        
        # CAS 1: QUE des mains de 4 cartes
        max_hands_4 = remaining_cards // 4
        if remaining_cards % 4 == 0:  # Division exacte
            arrangements['pure_4_only'] = self.calculate_natural_constraints(max_hands_4)
            print(f"CAS 1 - {max_hands_4} mains de 4 cartes exactement:")
            print(f"  Arrangements exacts: {arrangements['pure_4_only']:,}")
        else:
            print(f"CAS 1 - {max_hands_4} mains de 4 cartes (reste {remaining_cards % 4}):")
            print(f"  Arrangements exacts: 0 (division non exacte)")
        
        # CAS 2: QUE des mains de 6 cartes
        max_hands_6 = remaining_cards // 6
        if remaining_cards % 6 == 0:  # Division exacte
            arrangements['pure_6_only'] = self.calculate_double_draw_constraints(max_hands_6)
            print(f"CAS 2 - {max_hands_6} mains de 6 cartes exactement:")
            print(f"  Arrangements exacts: {arrangements['pure_6_only']:,}")
        else:
            print(f"CAS 2 - {max_hands_6} mains de 6 cartes (reste {remaining_cards % 6}):")
            print(f"  Arrangements exacts: 0 (division non exacte)")
        
        # CAS 3: Mélange de mains 4 et 6 cartes
        print(f"CAS 3 - Mélange de mains 4 et 6 cartes:")
        mixed_total = 0
        
        for hands_4 in range(1, remaining_cards // 4):
            cards_after_4 = remaining_cards - (hands_4 * 4)
            if cards_after_4 % 6 == 0 and cards_after_4 > 0:
                hands_6 = cards_after_4 // 6
                if hands_6 > 0:  # Au moins une main de 6
                    config_arrangements = self.calculate_mixed_constraints(hands_4, hands_6)
                    mixed_total += config_arrangements
                    
                    if config_arrangements > 0:
                        print(f"  {hands_4} mains de 4 + {hands_6} mains de 6: {config_arrangements:,}")
        
        arrangements['mixed_4_and_6'] = mixed_total
        print(f"  Total mélange: {mixed_total:,}")
        
        return arrangements
    
    def calculate_odd_arrangements_exact(self, remaining_cards):
        """Calcule les arrangements exacts pour brûlage impair"""
        arrangements = {
            'pure_5_only': 0
        }
        
        # CAS UNIQUE: QUE des mains de 5 cartes
        max_hands_5 = remaining_cards // 5
        if remaining_cards % 5 == 0:  # Division exacte
            arrangements['pure_5_only'] = self.calculate_single_draw_constraints(max_hands_5)
            print(f"CAS UNIQUE - {max_hands_5} mains de 5 cartes exactement:")
            print(f"  Arrangements exacts: {arrangements['pure_5_only']:,}")
        else:
            print(f"CAS UNIQUE - {max_hands_5} mains de 5 cartes (reste {remaining_cards % 5}):")
            print(f"  Arrangements exacts: 0 (division non exacte)")
        
        return arrangements
    
    def run_pure_mathematical_analysis(self):
        """Exécute l'analyse mathématique pure complète"""
        
        print("="*70)
        print("CALCULATEUR MATHÉMATIQUE PUR - BACCARAT")
        print("Équations exactes basées sur les contraintes de tirage")
        print("="*70)
        
        all_results = {}
        totals = {
            'pure_even_4_only': 0,
            'pure_even_6_only': 0,
            'pure_even_mixed': 0,
            'pure_odd_5_only': 0
        }
        
        # Analyser chaque configuration de brûlage
        for burn_cards in range(2, 12):
            result = self.analyze_burn_configuration_exact(burn_cards)
            all_results[burn_cards] = result
            
            # Additionner les totaux
            if result['burn_is_even']:
                totals['pure_even_4_only'] += result['arrangements'].get('pure_4_only', 0)
                totals['pure_even_6_only'] += result['arrangements'].get('pure_6_only', 0)
                totals['pure_even_mixed'] += result['arrangements'].get('mixed_4_and_6', 0)
            else:
                totals['pure_odd_5_only'] += result['arrangements'].get('pure_5_only', 0)
        
        print("\n" + "="*70)
        print("RÉSULTATS MATHÉMATIQUES EXACTS")
        print("="*70)
        print(f"1. Parties purement paires (QUE 4 cartes): {totals['pure_even_4_only']:,}")
        print(f"2. Parties purement paires (QUE 6 cartes): {totals['pure_even_6_only']:,}")
        print(f"3. Parties purement paires (mélange 4+6): {totals['pure_even_mixed']:,}")
        print(f"4. Parties purement impaires (QUE 5 cartes): {totals['pure_odd_5_only']:,}")
        
        total_pure_even = totals['pure_even_4_only'] + totals['pure_even_6_only'] + totals['pure_even_mixed']
        total_all = total_pure_even + totals['pure_odd_5_only']
        
        print(f"\nTOTAL parties purement paires: {total_pure_even:,}")
        print(f"TOTAL parties purement impaires: {totals['pure_odd_5_only']:,}")
        print(f"TOTAL GÉNÉRAL: {total_all:,}")
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE MATHÉMATIQUE PURE")
        print("="*70)
        print("✓ Équations exactes basées sur les contraintes de tirage")
        print("✓ Probabilités officielles du Wizard of Odds")
        print("✓ Calculs combinatoires C(n,k) pour les arrangements")
        print("✓ Contraintes multinomiales pour les mélanges")
        print("✓ Aucune simulation - calculs déterministes uniquement")
        
        return {
            'totals': totals,
            'details': all_results
        }

if __name__ == "__main__":
    calculator = BaccaratPureMathematicalCalculator()
    final_results = calculator.run_pure_mathematical_analysis()
