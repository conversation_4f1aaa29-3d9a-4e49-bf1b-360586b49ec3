#!/usr/bin/env python3
"""
Calculateur des probabilités des séquences maximales
Détermine la probabilité que chaque séquence maximale se produise
"""

import math
from fractions import Fraction

class BaccaratProbabilitiesCalculator:
    def __init__(self):
        self.total_cards = 312
        
        # Probabilités officielles basées sur les règles du Baccarat (8 decks)
        self.hand_probabilities = {
            'natural_4_cards': 0.458597,      # Naturels + aucune 3ème carte
            'single_draw_5_cards': 0.446247,  # Un seul joueur tire
            'double_draw_6_cards': 0.095156   # Les deux joueurs tirent
        }
        
        # Longueurs maximales par brûlage (du tableau précédent)
        self.max_lengths = {
            2: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            3: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            4: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            5: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            6: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            7: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            8: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            9: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            10: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            11: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0}
        }
        
        print("Calculateur des probabilités des séquences maximales")
        print("Basé sur les probabilités officielles du Baccarat")
    
    def calculate_probability_for_burn(self, burn_cards):
        """Calcule les probabilités pour un brûlage donné"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*60}")
        print(f"PROBABILITÉS POUR BRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'})")
        print(f"Cartes restantes: {remaining_cards}")
        print("="*60)
        
        results = {}
        
        if burn_is_even:
            results.update(self.calculate_even_probabilities(burn_cards))
        else:
            results.update(self.calculate_odd_probabilities(burn_cards))
        
        return results
    
    def calculate_even_probabilities(self, burn_cards):
        """Calcule les probabilités pour séquences paires"""
        results = {}
        max_data = self.max_lengths[burn_cards]
        
        # PROBABILITÉ ORDRE 4 (32 naturels consécutifs)
        if max_data['order_4'] > 0:
            print(f"ORDRE 4 - {max_data['order_4']} naturels consécutifs:")
            
            prob_natural = self.hand_probabilities['natural_4_cards']
            prob_sequence = prob_natural ** max_data['order_4']
            
            print(f"  Probabilité d'un naturel: {prob_natural:.6f}")
            print(f"  Probabilité de {max_data['order_4']} naturels consécutifs: {prob_sequence:.2e}")
            
            # Conversion en notation scientifique lisible
            if prob_sequence > 0:
                exponent = math.log10(prob_sequence)
                print(f"  Soit environ: 1 chance sur {1/prob_sequence:.2e}")
            
            results['order_4_probability'] = prob_sequence
        
        # PROBABILITÉ ORDRE 6 (40 doubles tirages consécutifs)
        if max_data['order_6'] > 0:
            print(f"\nORDRE 6 - {max_data['order_6']} doubles tirages consécutifs:")
            
            prob_double = self.hand_probabilities['double_draw_6_cards']
            prob_sequence = prob_double ** max_data['order_6']
            
            print(f"  Probabilité d'un double tirage: {prob_double:.6f}")
            print(f"  Probabilité de {max_data['order_6']} doubles tirages consécutifs: {prob_sequence:.2e}")
            
            if prob_sequence > 0:
                print(f"  Soit environ: 1 chance sur {1/prob_sequence:.2e}")
            
            results['order_6_probability'] = prob_sequence
        
        # PROBABILITÉ MIXTE 4+6
        if max_data['mixed_4_6'] > 0:
            print(f"\nMIXTE 4+6 - {max_data['mixed_4_6']} mains consécutives:")
            
            # Estimation pour séquence mixte optimale (exemple: 31×4 + 31×6)
            hands_4_estimated = max_data['mixed_4_6'] // 2
            hands_6_estimated = max_data['mixed_4_6'] - hands_4_estimated
            
            prob_4 = self.hand_probabilities['natural_4_cards']
            prob_6 = self.hand_probabilities['double_draw_6_cards']
            
            # Probabilité d'une séquence mixte spécifique
            prob_sequence = (prob_4 ** hands_4_estimated) * (prob_6 ** hands_6_estimated)
            
            # Facteur combinatoire pour l'ordre des mains
            from math import factorial
            combinatorial_factor = factorial(max_data['mixed_4_6']) / (factorial(hands_4_estimated) * factorial(hands_6_estimated))
            
            # Probabilité totale (toutes les séquences mixtes possibles)
            total_prob = prob_sequence * combinatorial_factor
            
            print(f"  Composition estimée: {hands_4_estimated} mains de 4 + {hands_6_estimated} mains de 6")
            print(f"  Probabilité d'une séquence spécifique: {prob_sequence:.2e}")
            print(f"  Facteur combinatoire: {combinatorial_factor:.2e}")
            print(f"  Probabilité totale: {total_prob:.2e}")
            
            if total_prob > 0:
                print(f"  Soit environ: 1 chance sur {1/total_prob:.2e}")
            
            results['mixed_4_6_probability'] = total_prob
        
        return results
    
    def calculate_odd_probabilities(self, burn_cards):
        """Calcule les probabilités pour séquences impaires"""
        results = {}
        max_data = self.max_lengths[burn_cards]
        
        # PROBABILITÉ ORDRE 5 (60 tirages uniques consécutifs)
        if max_data['order_5'] > 0:
            print(f"ORDRE 5 - {max_data['order_5']} tirages uniques consécutifs:")
            
            prob_single = self.hand_probabilities['single_draw_5_cards']
            prob_sequence = prob_single ** max_data['order_5']
            
            print(f"  Probabilité d'un tirage unique: {prob_single:.6f}")
            print(f"  Probabilité de {max_data['order_5']} tirages uniques consécutifs: {prob_sequence:.2e}")
            
            if prob_sequence > 0:
                print(f"  Soit environ: 1 chance sur {1/prob_sequence:.2e}")
            
            results['order_5_probability'] = prob_sequence
        
        return results
    
    def run_complete_probability_analysis(self):
        """Exécute l'analyse complète des probabilités"""
        
        print("="*70)
        print("CALCULATEUR DES PROBABILITÉS DES SÉQUENCES MAXIMALES")
        print("Probabilités que chaque séquence maximale se produise")
        print("="*70)
        
        print("\nPROBABILITÉS DE BASE:")
        print(f"- Naturel (4 cartes): {self.hand_probabilities['natural_4_cards']:.6f}")
        print(f"- Tirage unique (5 cartes): {self.hand_probabilities['single_draw_5_cards']:.6f}")
        print(f"- Double tirage (6 cartes): {self.hand_probabilities['double_draw_6_cards']:.6f}")
        
        all_results = {}
        
        # Analyser chaque brûlage
        for burn_cards in range(2, 12):
            result = self.calculate_probability_for_burn(burn_cards)
            all_results[burn_cards] = result
        
        print("\n" + "="*70)
        print("TABLEAU RÉCAPITULATIF DES PROBABILITÉS")
        print("="*70)
        print("Brûlage | Ordre 4      | Ordre 6      | Ordre 5      | Mixte 4+6")
        print("-" * 70)
        
        for burn_cards in range(2, 12):
            result = all_results[burn_cards]
            burn_is_even = (burn_cards % 2 == 0)
            
            if burn_is_even:
                prob_4 = result.get('order_4_probability', 0)
                prob_6 = result.get('order_6_probability', 0)
                prob_mixed = result.get('mixed_4_6_probability', 0)
                
                print(f"   {burn_cards:2d}   | {prob_4:.2e} | {prob_6:.2e} |      -       | {prob_mixed:.2e}")
            else:
                prob_5 = result.get('order_5_probability', 0)
                print(f"   {burn_cards:2d}   |      -       |      -       | {prob_5:.2e} |      -")
        
        print("\n" + "="*70)
        print("INTERPRÉTATION DES PROBABILITÉS")
        print("="*70)
        
        # Trouver les probabilités les plus élevées
        max_prob_4 = max((r.get('order_4_probability', 0) for r in all_results.values()), default=0)
        max_prob_6 = max((r.get('order_6_probability', 0) for r in all_results.values()), default=0)
        max_prob_5 = max((r.get('order_5_probability', 0) for r in all_results.values()), default=0)
        max_prob_mixed = max((r.get('mixed_4_6_probability', 0) for r in all_results.values()), default=0)
        
        print(f"1. Séquence la plus probable ORDRE 4: {max_prob_4:.2e}")
        if max_prob_4 > 0:
            print(f"   Soit 1 chance sur {1/max_prob_4:.2e}")
        
        print(f"\n2. Séquence la plus probable ORDRE 6: {max_prob_6:.2e}")
        if max_prob_6 > 0:
            print(f"   Soit 1 chance sur {1/max_prob_6:.2e}")
        
        print(f"\n3. Séquence la plus probable ORDRE 5: {max_prob_5:.2e}")
        if max_prob_5 > 0:
            print(f"   Soit 1 chance sur {1/max_prob_5:.2e}")
        
        print(f"\n4. Séquence la plus probable MIXTE 4+6: {max_prob_mixed:.2e}")
        if max_prob_mixed > 0:
            print(f"   Soit 1 chance sur {1/max_prob_mixed:.2e}")
        
        print("\n" + "="*70)
        print("CONCLUSION")
        print("="*70)
        print("Toutes ces probabilités sont EXTRÊMEMENT faibles.")
        print("Les séquences maximales sont théoriquement possibles mais")
        print("pratiquement impossibles à observer dans la réalité.")
        
        return {
            'results_by_burn': all_results,
            'max_probabilities': {
                'order_4': max_prob_4,
                'order_6': max_prob_6,
                'order_5': max_prob_5,
                'mixed_4_6': max_prob_mixed
            }
        }

if __name__ == "__main__":
    calculator = BaccaratProbabilitiesCalculator()
    final_results = calculator.run_complete_probability_analysis()
