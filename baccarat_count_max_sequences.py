#!/usr/bin/env python3
"""
Compteur du nombre de séquences maximales possibles
Détermine combien de séquences maximales distinctes existent pour chaque brûlage
"""

class BaccaratCountMaxSequences:
    def __init__(self):
        self.total_cards = 312
        
        # Composition exacte du deck (8 jeux de 52 cartes)
        self.deck_composition = {
            0: 128,  # 10, J, Q, K (valeur 0)
            1: 32,   # As (valeur 1)
            2: 32, 3: 32, 4: 32, 5: 32, 6: 32, 7: 32, 8: 32, 9: 32
        }
        
        print("Compteur du nombre de séquences maximales possibles")
        print(f"Total cartes: {self.total_cards}")
        print("Objectif: Compter les séquences maximales distinctes par brûlage")
    
    def count_sequences_for_burn(self, burn_cards):
        """Compte les séquences maximales pour un brûlage donné"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*60}")
        print(f"BRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'})")
        print(f"Cartes restantes: {remaining_cards}")
        print("="*60)
        
        if burn_is_even:
            return self.count_even_sequences(remaining_cards, burn_cards)
        else:
            return self.count_odd_sequences(remaining_cards, burn_cards)
    
    def count_even_sequences(self, remaining_cards, burn_cards):
        """Compte les séquences paires maximales possibles"""
        results = {
            'count_order_4': 0,
            'count_order_6': 0,
            'count_mixed_4_6': 0,
            'max_length_4': 0,
            'max_length_6': 0,
            'max_length_mixed': 0
        }
        
        # COMPTER LES SÉQUENCES D'ORDRE 4 MAXIMALES
        print("SÉQUENCES MAXIMALES D'ORDRE 4 (que des naturels):")
        
        # Limite réelle basée sur les cartes 8/9
        cards_8_9 = self.deck_composition[8] + self.deck_composition[9]
        max_naturals = min(remaining_cards // 4, cards_8_9 // 2)
        
        # Vérifier si une séquence maximale est possible
        if max_naturals > 0:
            cards_needed = max_naturals * 4
            if cards_needed <= remaining_cards:
                results['count_order_4'] = 1  # Une seule séquence maximale possible
                results['max_length_4'] = max_naturals
                print(f"  Longueur maximale: {max_naturals} mains")
                print(f"  Nombre de séquences maximales: 1")
                print(f"  Cartes utilisées: {cards_needed}")
            else:
                print(f"  Aucune séquence maximale possible")
        else:
            print(f"  Aucune séquence maximale possible")
        
        # COMPTER LES SÉQUENCES D'ORDRE 6 MAXIMALES
        print("\nSÉQUENCES MAXIMALES D'ORDRE 6 (que des doubles tirages):")
        
        # Limite réelle basée sur les contraintes de double tirage
        max_doubles = min(remaining_cards // 6, remaining_cards // 8)  # Estimation conservative
        
        if max_doubles > 0:
            cards_needed = max_doubles * 6
            if cards_needed <= remaining_cards:
                results['count_order_6'] = 1  # Une seule séquence maximale possible
                results['max_length_6'] = max_doubles
                print(f"  Longueur maximale: {max_doubles} mains")
                print(f"  Nombre de séquences maximales: 1")
                print(f"  Cartes utilisées: {cards_needed}")
            else:
                print(f"  Aucune séquence maximale possible")
        else:
            print(f"  Aucune séquence maximale possible")
        
        # COMPTER LES SÉQUENCES MIXTES 4+6 MAXIMALES
        print("\nSÉQUENCES MAXIMALES MIXTES 4+6:")
        
        mixed_sequences = self.count_mixed_max_sequences(remaining_cards, max_naturals, max_doubles)
        results['count_mixed_4_6'] = mixed_sequences['count']
        results['max_length_mixed'] = mixed_sequences['max_length']
        
        print(f"  Longueur maximale: {mixed_sequences['max_length']} mains")
        print(f"  Nombre de séquences maximales: {mixed_sequences['count']}")
        
        if mixed_sequences['count'] > 0:
            print("  Exemples de compositions maximales:")
            for i, combo in enumerate(mixed_sequences['combinations'][:3]):
                print(f"    {i+1}. {combo['hands_4']} mains de 4 + {combo['hands_6']} mains de 6")
        
        return results
    
    def count_odd_sequences(self, remaining_cards, burn_cards):
        """Compte les séquences impaires maximales possibles"""
        results = {
            'count_order_5': 0,
            'max_length_5': 0
        }
        
        # COMPTER LES SÉQUENCES D'ORDRE 5 MAXIMALES
        print("SÉQUENCES MAXIMALES D'ORDRE 5 (que des tirages uniques):")
        
        # Limite réelle basée sur les contraintes de tirage unique
        max_singles = min(remaining_cards // 5, remaining_cards // 6)  # Estimation conservative
        
        if max_singles > 0:
            cards_needed = max_singles * 5
            if cards_needed <= remaining_cards:
                results['count_order_5'] = 1  # Une seule séquence maximale possible
                results['max_length_5'] = max_singles
                print(f"  Longueur maximale: {max_singles} mains")
                print(f"  Nombre de séquences maximales: 1")
                print(f"  Cartes utilisées: {cards_needed}")
            else:
                print(f"  Aucune séquence maximale possible")
        else:
            print(f"  Aucune séquence maximale possible")
        
        return results
    
    def count_mixed_max_sequences(self, remaining_cards, max_naturals, max_doubles):
        """Compte les séquences mixtes 4+6 de longueur maximale"""
        max_length = 0
        max_combinations = []
        
        # Énumérer toutes les combinaisons possibles
        for hands_4 in range(0, max_naturals + 1):
            cards_after_4 = remaining_cards - (hands_4 * 4)
            max_hands_6_possible = min(max_doubles, cards_after_4 // 6)
            
            for hands_6 in range(0, max_hands_6_possible + 1):
                total_hands = hands_4 + hands_6
                cards_used = (hands_4 * 4) + (hands_6 * 6)
                
                # Vérifier que c'est un vrai mélange
                if (cards_used <= remaining_cards and
                    hands_4 > 0 and hands_6 > 0):  # Vrai mélange
                    
                    if total_hands > max_length:
                        # Nouvelle longueur maximale trouvée
                        max_length = total_hands
                        max_combinations = [{
                            'hands_4': hands_4,
                            'hands_6': hands_6,
                            'total_hands': total_hands,
                            'cards_used': cards_used
                        }]
                    elif total_hands == max_length:
                        # Même longueur maximale, ajouter à la liste
                        max_combinations.append({
                            'hands_4': hands_4,
                            'hands_6': hands_6,
                            'total_hands': total_hands,
                            'cards_used': cards_used
                        })
        
        return {
            'count': len(max_combinations),
            'max_length': max_length,
            'combinations': max_combinations
        }
    
    def run_complete_count(self):
        """Exécute le comptage complet pour tous les brûlages"""
        
        print("="*70)
        print("COMPTEUR DU NOMBRE DE SÉQUENCES MAXIMALES POSSIBLES")
        print("Nombre de séquences maximales distinctes par brûlage")
        print("="*70)
        
        all_results = {}
        totals = {
            'total_order_4_sequences': 0,
            'total_order_6_sequences': 0,
            'total_order_5_sequences': 0,
            'total_mixed_4_6_sequences': 0,
            'global_max_length_4': 0,
            'global_max_length_6': 0,
            'global_max_length_5': 0,
            'global_max_length_mixed': 0
        }
        
        # Analyser chaque configuration de brûlage
        for burn_cards in range(2, 12):
            result = self.count_sequences_for_burn(burn_cards)
            all_results[burn_cards] = result
            
            burn_is_even = (burn_cards % 2 == 0)
            
            if burn_is_even:
                # Additionner les séquences paires
                totals['total_order_4_sequences'] += result.get('count_order_4', 0)
                totals['total_order_6_sequences'] += result.get('count_order_6', 0)
                totals['total_mixed_4_6_sequences'] += result.get('count_mixed_4_6', 0)
                
                # Mettre à jour les longueurs maximales globales
                totals['global_max_length_4'] = max(totals['global_max_length_4'], 
                                                   result.get('max_length_4', 0))
                totals['global_max_length_6'] = max(totals['global_max_length_6'], 
                                                   result.get('max_length_6', 0))
                totals['global_max_length_mixed'] = max(totals['global_max_length_mixed'], 
                                                       result.get('max_length_mixed', 0))
            else:
                # Additionner les séquences impaires
                totals['total_order_5_sequences'] += result.get('count_order_5', 0)
                
                # Mettre à jour la longueur maximale globale
                totals['global_max_length_5'] = max(totals['global_max_length_5'], 
                                                   result.get('max_length_5', 0))
        
        print("\n" + "="*70)
        print("RÉSULTATS FINAUX - NOMBRE DE SÉQUENCES MAXIMALES")
        print("="*70)
        print(f"1. Séquences maximales d'ORDRE 4: {totals['total_order_4_sequences']}")
        print(f"   Longueur maximale globale: {totals['global_max_length_4']} mains")
        
        print(f"\n2. Séquences maximales d'ORDRE 6: {totals['total_order_6_sequences']}")
        print(f"   Longueur maximale globale: {totals['global_max_length_6']} mains")
        
        print(f"\n3. Séquences maximales d'ORDRE 5: {totals['total_order_5_sequences']}")
        print(f"   Longueur maximale globale: {totals['global_max_length_5']} mains")
        
        print(f"\n4. Séquences maximales MIXTES 4+6: {totals['total_mixed_4_6_sequences']}")
        print(f"   Longueur maximale globale: {totals['global_max_length_mixed']} mains")
        
        total_pure_even = (totals['total_order_4_sequences'] + 
                          totals['total_order_6_sequences'] + 
                          totals['total_mixed_4_6_sequences'])
        total_all = total_pure_even + totals['total_order_5_sequences']
        
        print(f"\nTOTAL séquences maximales purement paires: {total_pure_even}")
        print(f"TOTAL séquences maximales purement impaires: {totals['total_order_5_sequences']}")
        print(f"TOTAL GÉNÉRAL: {total_all}")
        
        print("\n" + "="*70)
        print("TABLEAU RÉCAPITULATIF PAR BRÛLAGE")
        print("="*70)
        print("Brûlage | Cartes | Ordre 4 | Ordre 6 | Ordre 5 | Mixte 4+6")
        print("-" * 60)
        
        for burn_cards in range(2, 12):
            result = all_results[burn_cards]
            remaining = 312 - burn_cards
            burn_is_even = (burn_cards % 2 == 0)
            
            if burn_is_even:
                print(f"   {burn_cards:2d}   |  {remaining:3d}   |    {result.get('count_order_4', 0):1d}    |    {result.get('count_order_6', 0):1d}    |    -    |     {result.get('count_mixed_4_6', 0):1d}")
            else:
                print(f"   {burn_cards:2d}   |  {remaining:3d}   |    -    |    -    |    {result.get('count_order_5', 0):1d}    |     -")
        
        return {
            'totals': totals,
            'details': all_results
        }

if __name__ == "__main__":
    calculator = BaccaratCountMaxSequences()
    final_results = calculator.run_complete_count()
