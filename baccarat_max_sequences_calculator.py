#!/usr/bin/env python3
"""
Calculateur des séquences maximales possibles par brûlage
Détermine la longueur maximale des séquences pures pour chaque configuration
"""

class BaccaratMaxSequencesCalculator:
    def __init__(self):
        self.total_cards = 312
        
        # Contraintes réalistes basées sur les règles officielles
        self.max_consecutive_constraints = {
            'naturals_4': 25,      # Maximum de naturels consécutifs réalistes
            'double_draws_6': 20,  # Maximum de doubles tirages consécutifs
            'single_draws_5': 35,  # Maximum de tirages uniques consécutifs
            'mixed_4_6': 50        # Maximum pour séquences mixtes paires
        }
        
        print("Calculateur des séquences maximales par brûlage")
        print(f"Total cartes: {self.total_cards}")
        print("Objectif: Longueur maximale des séquences pures")
        print("\nContraintes réalistes:")
        for key, value in self.max_consecutive_constraints.items():
            print(f"  {key}: {value} mains consécutives max")
    
    def calculate_max_sequence_for_burn(self, burn_cards):
        """Calcule les séquences maximales pour un brûlage donné"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*60}")
        print(f"BRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'})")
        print(f"Cartes restantes: {remaining_cards}")
        print("="*60)
        
        if burn_is_even:
            return self.calculate_max_even_sequences(remaining_cards, burn_cards)
        else:
            return self.calculate_max_odd_sequences(remaining_cards, burn_cards)
    
    def calculate_max_even_sequences(self, remaining_cards, burn_cards):
        """Calcule les séquences maximales paires"""
        results = {
            'max_order_4': 0,
            'max_order_6': 0,
            'max_mixed_4_6': 0
        }
        
        # SÉQUENCE MAXIMALE ORDRE 4 (que des naturels)
        print("SÉQUENCE MAXIMALE ORDRE 4 (que des naturels):")
        max_naturals = min(
            self.max_consecutive_constraints['naturals_4'],
            remaining_cards // 4
        )
        cards_used_4 = max_naturals * 4
        cards_remaining_4 = remaining_cards - cards_used_4
        
        results['max_order_4'] = max_naturals
        print(f"  Longueur maximale: {max_naturals} mains consécutives")
        print(f"  Cartes utilisées: {cards_used_4}")
        print(f"  Cartes restantes: {cards_remaining_4}")
        
        # SÉQUENCE MAXIMALE ORDRE 6 (que des doubles tirages)
        print("\nSÉQUENCE MAXIMALE ORDRE 6 (que des doubles tirages):")
        max_double_draws = min(
            self.max_consecutive_constraints['double_draws_6'],
            remaining_cards // 6
        )
        cards_used_6 = max_double_draws * 6
        cards_remaining_6 = remaining_cards - cards_used_6
        
        results['max_order_6'] = max_double_draws
        print(f"  Longueur maximale: {max_double_draws} mains consécutives")
        print(f"  Cartes utilisées: {cards_used_6}")
        print(f"  Cartes restantes: {cards_remaining_6}")
        
        # SÉQUENCE MAXIMALE MIXTE 4+6
        print("\nSÉQUENCE MAXIMALE MIXTE 4+6 (purement paires):")
        max_mixed = self.calculate_max_mixed_sequence(remaining_cards)
        results['max_mixed_4_6'] = max_mixed['total_hands']
        
        print(f"  Longueur maximale: {max_mixed['total_hands']} mains consécutives")
        print(f"  Composition: {max_mixed['hands_4']} mains de 4 + {max_mixed['hands_6']} mains de 6")
        print(f"  Cartes utilisées: {max_mixed['cards_used']}")
        print(f"  Cartes restantes: {remaining_cards - max_mixed['cards_used']}")
        
        return results
    
    def calculate_max_odd_sequences(self, remaining_cards, burn_cards):
        """Calcule les séquences maximales impaires"""
        results = {
            'max_order_5': 0
        }
        
        # SÉQUENCE MAXIMALE ORDRE 5 (que des tirages uniques)
        print("SÉQUENCE MAXIMALE ORDRE 5 (que des tirages uniques):")
        max_single_draws = min(
            self.max_consecutive_constraints['single_draws_5'],
            remaining_cards // 5
        )
        cards_used_5 = max_single_draws * 5
        cards_remaining_5 = remaining_cards - cards_used_5
        
        results['max_order_5'] = max_single_draws
        print(f"  Longueur maximale: {max_single_draws} mains consécutives")
        print(f"  Cartes utilisées: {cards_used_5}")
        print(f"  Cartes restantes: {cards_remaining_5}")
        
        return results
    
    def calculate_max_mixed_sequence(self, remaining_cards):
        """Calcule la séquence mixte 4+6 la plus longue possible"""
        max_total_hands = 0
        best_combination = {'hands_4': 0, 'hands_6': 0, 'total_hands': 0, 'cards_used': 0}
        
        # Contrainte globale sur le nombre total de mains mixtes
        max_mixed_hands = min(
            self.max_consecutive_constraints['mixed_4_6'],
            remaining_cards // 4  # Au minimum 4 cartes par main
        )
        
        # Énumérer les combinaisons optimales
        for hands_4 in range(0, min(25, remaining_cards // 4)):  # Max 25 naturels
            cards_after_4 = remaining_cards - (hands_4 * 4)
            max_hands_6 = min(20, cards_after_4 // 6)  # Max 20 doubles tirages
            
            for hands_6 in range(0, max_hands_6 + 1):
                total_hands = hands_4 + hands_6
                cards_used = (hands_4 * 4) + (hands_6 * 6)
                
                # Vérifier les contraintes
                if (total_hands <= max_mixed_hands and
                    cards_used <= remaining_cards and
                    total_hands > max_total_hands and
                    hands_4 > 0 and hands_6 > 0):  # Vrai mélange
                    
                    max_total_hands = total_hands
                    best_combination = {
                        'hands_4': hands_4,
                        'hands_6': hands_6,
                        'total_hands': total_hands,
                        'cards_used': cards_used
                    }
        
        return best_combination
    
    def run_max_sequences_analysis(self):
        """Exécute l'analyse des séquences maximales pour tous les brûlages"""
        
        print("="*70)
        print("CALCULATEUR DES SÉQUENCES MAXIMALES PAR BRÛLAGE")
        print("Longueur maximale des séquences pures possibles")
        print("="*70)
        
        all_results = {}
        summary = {
            'max_order_4_global': 0,
            'max_order_6_global': 0,
            'max_order_5_global': 0,
            'max_mixed_4_6_global': 0,
            'best_burn_4': 0,
            'best_burn_6': 0,
            'best_burn_5': 0,
            'best_burn_mixed': 0
        }
        
        # Analyser chaque configuration de brûlage
        for burn_cards in range(2, 12):
            result = self.calculate_max_sequence_for_burn(burn_cards)
            all_results[burn_cards] = result
            
            burn_is_even = (burn_cards % 2 == 0)
            
            if burn_is_even:
                # Mettre à jour les maximums pour les séquences paires
                if result['max_order_4'] > summary['max_order_4_global']:
                    summary['max_order_4_global'] = result['max_order_4']
                    summary['best_burn_4'] = burn_cards
                
                if result['max_order_6'] > summary['max_order_6_global']:
                    summary['max_order_6_global'] = result['max_order_6']
                    summary['best_burn_6'] = burn_cards
                
                if result['max_mixed_4_6'] > summary['max_mixed_4_6_global']:
                    summary['max_mixed_4_6_global'] = result['max_mixed_4_6']
                    summary['best_burn_mixed'] = burn_cards
            else:
                # Mettre à jour les maximums pour les séquences impaires
                if result['max_order_5'] > summary['max_order_5_global']:
                    summary['max_order_5_global'] = result['max_order_5']
                    summary['best_burn_5'] = burn_cards
        
        print("\n" + "="*70)
        print("RÉSUMÉ DES SÉQUENCES MAXIMALES GLOBALES")
        print("="*70)
        print(f"1. Séquence maximale ORDRE 4: {summary['max_order_4_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_4']} (cartes restantes: {312 - summary['best_burn_4']})")
        
        print(f"\n2. Séquence maximale ORDRE 6: {summary['max_order_6_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_6']} (cartes restantes: {312 - summary['best_burn_6']})")
        
        print(f"\n3. Séquence maximale ORDRE 5: {summary['max_order_5_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_5']} (cartes restantes: {312 - summary['best_burn_5']})")
        
        print(f"\n4. Séquence maximale MIXTE 4+6: {summary['max_mixed_4_6_global']} mains")
        print(f"   Meilleur brûlage: {summary['best_burn_mixed']} (cartes restantes: {312 - summary['best_burn_mixed']})")
        
        print("\n" + "="*70)
        print("TABLEAU RÉCAPITULATIF PAR BRÛLAGE")
        print("="*70)
        print("Brûlage | Cartes | Ordre 4 | Ordre 6 | Ordre 5 | Mixte 4+6")
        print("-" * 60)
        
        for burn_cards in range(2, 12):
            result = all_results[burn_cards]
            remaining = 312 - burn_cards
            burn_is_even = (burn_cards % 2 == 0)
            
            if burn_is_even:
                print(f"   {burn_cards:2d}   |  {remaining:3d}   |   {result['max_order_4']:2d}    |   {result['max_order_6']:2d}    |    -    |    {result['max_mixed_4_6']:2d}")
            else:
                print(f"   {burn_cards:2d}   |  {remaining:3d}   |    -    |    -    |   {result['max_order_5']:2d}    |     -")
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE")
        print("="*70)
        print("✓ Contraintes réalistes basées sur les règles officielles")
        print("✓ Pas de limitation par le nombre total de mains")
        print("✓ Calcul de la longueur maximale théorique")
        print("✓ Prise en compte des cartes restantes après chaque séquence")
        print("✓ Optimisation des combinaisons mixtes 4+6")
        
        return {
            'summary': summary,
            'details': all_results
        }

if __name__ == "__main__":
    calculator = BaccaratMaxSequencesCalculator()
    final_results = calculator.run_max_sequences_analysis()
