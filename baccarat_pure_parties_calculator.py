#!/usr/bin/env python3
"""
Calculateur direct des parties purement paires/impaires au Baccarat
Application directe des règles officielles
"""

import random
from collections import defaultdict

class BaccaratPurePartiesCalculator:
    def __init__(self):
        # Configuration selon les règles officielles
        self.total_deck_cards = 416  # 8 jeux de 52 cartes
        self.cards_per_game = 312    # Cut card aux 3/4
        
        # Création du deck complet
        self.full_deck = self.create_full_deck()
        
        # Valeurs Baccarat officielles
        self.card_values = {
            'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
            '10': 0, 'J': 0, 'Q': 0, 'K': 0
        }
        
        print("Calculateur de parties purement paires/impaires")
        print(f"Deck: {self.total_deck_cards} cartes")
        print(f"Cartes par partie: {self.cards_per_game}")
    
    def create_full_deck(self):
        """Crée le deck complet de 8 jeux"""
        ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
        deck = []
        for _ in range(8):  # 8 jeux
            for _ in range(4):  # 4 couleurs
                for rank in ranks:
                    deck.append(rank)
        return deck
    
    def get_baccarat_value(self, card):
        """Valeur Baccarat d'une carte"""
        return self.card_values[card]
    
    def calculate_total(self, cards):
        """Calcule le total d'une main (modulo 10)"""
        return sum(self.get_baccarat_value(card) for card in cards) % 10
    
    def apply_burn_rules(self, deck):
        """Applique les règles de brûlage officielles"""
        if not deck:
            return [], 0
        
        # Première carte détermine le brûlage
        burn_card = deck[0]
        burn_value = self.get_baccarat_value(burn_card)
        
        # Règles officielles de brûlage
        if burn_card in ['10', 'J', 'Q', 'K']:
            total_burned = 11  # 1 + 10 supplémentaires
        else:
            total_burned = 1 + burn_value  # 1 + valeur de la carte
        
        remaining_deck = deck[total_burned:]
        return remaining_deck, total_burned
    
    def should_player_draw(self, player_total):
        """Règles de tirage du joueur"""
        if player_total in [8, 9]:  # Naturel
            return False
        return player_total <= 5  # 0-5 tire, 6-7 reste
    
    def should_banker_draw(self, banker_total, player_total, player_third_value=None):
        """Règles de tirage du banquier selon tableau officiel"""
        if banker_total in [8, 9]:  # Naturel
            return False
        
        # Si joueur n'a pas tiré
        if player_third_value is None:
            return banker_total <= 5
        
        # Tableau officiel complet
        if banker_total == 7:
            return False
        elif banker_total == 6:
            return player_third_value in [6, 7]
        elif banker_total == 5:
            return player_third_value in [4, 5, 6, 7]
        elif banker_total == 4:
            return player_third_value in [2, 3, 4, 5, 6, 7]
        elif banker_total == 3:
            return player_third_value != 8
        else:  # 0, 1, 2
            return True
    
    def play_hand(self, deck, start_pos):
        """Joue une main complète selon les règles officielles"""
        if start_pos + 4 > len(deck):
            return None, start_pos
        
        # Distribution initiale : P-B-P-B
        player_cards = [deck[start_pos], deck[start_pos + 2]]
        banker_cards = [deck[start_pos + 1], deck[start_pos + 3]]
        
        player_total = self.calculate_total(player_cards)
        banker_total = self.calculate_total(banker_cards)
        
        cards_used = 4
        current_pos = start_pos + 4
        player_third_value = None
        
        # Vérification naturels
        if player_total in [8, 9] or banker_total in [8, 9]:
            winner = 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
            return {
                'cards_used': cards_used,
                'winner': winner,
                'player_total': player_total,
                'banker_total': banker_total
            }, current_pos
        
        # Tirage joueur
        if self.should_player_draw(player_total):
            if current_pos < len(deck):
                third_card = deck[current_pos]
                player_third_value = self.get_baccarat_value(third_card)
                player_total = (player_total + player_third_value) % 10
                cards_used += 1
                current_pos += 1
        
        # Tirage banquier
        if self.should_banker_draw(banker_total, player_total, player_third_value):
            if current_pos < len(deck):
                banker_third_value = self.get_baccarat_value(deck[current_pos])
                banker_total = (banker_total + banker_third_value) % 10
                cards_used += 1
                current_pos += 1
        
        winner = 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
        
        return {
            'cards_used': cards_used,
            'winner': winner,
            'player_total': player_total,
            'banker_total': banker_total
        }, current_pos
    
    def analyze_game(self, deck):
        """Analyse une partie complète"""
        # Appliquer brûlage
        remaining_deck, burn_cards = self.apply_burn_rules(deck)
        
        if len(remaining_deck) < 4:
            return None
        
        # Analyser la parité du brûlage
        burn_is_even = (burn_cards % 2 == 0)
        
        # Jouer toutes les manches
        hands = []
        current_pos = 0
        all_hands_even = True
        all_hands_odd = True
        
        while current_pos < len(remaining_deck) - 3:
            hand_result, new_pos = self.play_hand(remaining_deck, current_pos)
            
            if hand_result is None:
                break
            
            hands.append(hand_result)
            
            # Vérifier parité de cette manche
            hand_is_even = (hand_result['cards_used'] % 2 == 0)
            
            if not hand_is_even:
                all_hands_even = False
            if hand_is_even:
                all_hands_odd = False
            
            current_pos = new_pos
        
        return {
            'burn_cards': burn_cards,
            'burn_is_even': burn_is_even,
            'hands': hands,
            'total_hands': len(hands),
            'all_hands_even': all_hands_even,
            'all_hands_odd': all_hands_odd,
            'is_purely_even': burn_is_even and all_hands_even,
            'is_purely_odd': (not burn_is_even) and all_hands_odd
        }
    
    def count_pure_parties(self, num_simulations=1000000):
        """Compte les parties pures par simulation"""
        purely_even_count = 0
        purely_odd_count = 0
        purely_even_pair4_count = 0
        purely_even_pair6_count = 0
        
        print(f"\nSimulation de {num_simulations:,} parties...")
        
        for i in range(num_simulations):
            if i % 100000 == 0:
                print(f"Progression: {i:,}/{num_simulations:,}")
            
            # Mélanger et prendre 312 cartes
            deck_copy = self.full_deck.copy()
            random.shuffle(deck_copy)
            game_deck = deck_copy[:self.cards_per_game]
            
            # Analyser la partie
            analysis = self.analyze_game(game_deck)
            
            if analysis is None:
                continue
            
            # Compter les parties pures
            if analysis['is_purely_even']:
                purely_even_count += 1
                
                # Vérifier si toutes les mains ont 4 cartes
                all_4_cards = all(hand['cards_used'] == 4 for hand in analysis['hands'])
                if all_4_cards:
                    purely_even_pair4_count += 1
                
                # Vérifier si toutes les mains ont 6 cartes
                all_6_cards = all(hand['cards_used'] == 6 for hand in analysis['hands'])
                if all_6_cards:
                    purely_even_pair6_count += 1
            
            if analysis['is_purely_odd']:
                purely_odd_count += 1
        
        return {
            'purely_even': purely_even_count,
            'purely_odd': purely_odd_count,
            'purely_even_pair4': purely_even_pair4_count,
            'purely_even_pair6': purely_even_pair6_count,
            'total_simulations': num_simulations
        }
    
    def run_calculation(self):
        """Exécute le calcul complet"""
        print("="*70)
        print("CALCULATEUR DE PARTIES PUREMENT PAIRES/IMPAIRES")
        print("Application directe des règles officielles du Baccarat")
        print("="*70)
        
        # Simulation
        results = self.count_pure_parties(1000000)
        
        print("\n" + "="*70)
        print("RÉSULTATS FINAUX")
        print("="*70)
        print(f"Simulations effectuées: {results['total_simulations']:,}")
        print()
        print(f"Parties purement paires: {results['purely_even']:,}")
        print(f"Parties purement impaires: {results['purely_odd']:,}")
        print(f"Parties paires avec toutes mains 4 cartes: {results['purely_even_pair4']:,}")
        print(f"Parties paires avec toutes mains 6 cartes: {results['purely_even_pair6']:,}")
        
        # Calcul des probabilités
        if results['total_simulations'] > 0:
            prob_even = results['purely_even'] / results['total_simulations']
            prob_odd = results['purely_odd'] / results['total_simulations']
            
            print(f"\nProbabilités:")
            print(f"Parties purement paires: {prob_even:.8f} ({prob_even*100:.6f}%)")
            print(f"Parties purement impaires: {prob_odd:.8f} ({prob_odd*100:.6f}%)")
            
            if prob_even > 0:
                print(f"Fréquence parties paires: 1 sur {1/prob_even:,.0f}")
            if prob_odd > 0:
                print(f"Fréquence parties impaires: 1 sur {1/prob_odd:,.0f}")
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE")
        print("="*70)
        print("✓ Règles officielles du Baccarat strictement appliquées")
        print("✓ Brûlage selon la première carte (2-11 cartes)")
        print("✓ Tableau de tirage officiel complet")
        print("✓ 312 cartes par partie (cut card aux 3/4)")
        print("✓ Comptage direct des parties pures")
        print("✓ Simulation Monte Carlo sur 1 million de parties")
        
        return results

if __name__ == "__main__":
    calculator = BaccaratPurePartiesCalculator()
    final_results = calculator.run_calculation()
