#!/usr/bin/env python3
"""
Calculateur du nombre total de mains dans toutes les séquences maximales
"""

class BaccaratTotalHandsCalculator:
    def __init__(self):
        # Résultats des séquences maximales par brûlage
        self.sequences_data = {
            2: {'order_4': 1, 'order_6': 1, 'order_5': 0, 'mixed_4_6': 2},
            3: {'order_4': 0, 'order_6': 0, 'order_5': 1, 'mixed_4_6': 0},
            4: {'order_4': 1, 'order_6': 1, 'order_5': 0, 'mixed_4_6': 1},
            5: {'order_4': 0, 'order_6': 0, 'order_5': 1, 'mixed_4_6': 0},
            6: {'order_4': 1, 'order_6': 1, 'order_5': 0, 'mixed_4_6': 3},
            7: {'order_4': 0, 'order_6': 0, 'order_5': 1, 'mixed_4_6': 0},
            8: {'order_4': 1, 'order_6': 1, 'order_5': 0, 'mixed_4_6': 2},
            9: {'order_4': 0, 'order_6': 0, 'order_5': 1, 'mixed_4_6': 0},
            10: {'order_4': 1, 'order_6': 1, 'order_5': 0, 'mixed_4_6': 1},
            11: {'order_4': 0, 'order_6': 0, 'order_5': 1, 'mixed_4_6': 0}
        }
        
        # Longueurs maximales par type et brûlage
        self.max_lengths = {
            2: {'order_4': 32, 'order_6': 38, 'order_5': 0, 'mixed_4_6': 62},
            3: {'order_4': 0, 'order_6': 0, 'order_5': 51, 'mixed_4_6': 0},
            4: {'order_4': 32, 'order_6': 38, 'order_5': 0, 'mixed_4_6': 62},
            5: {'order_4': 0, 'order_6': 0, 'order_5': 51, 'mixed_4_6': 0},
            6: {'order_4': 32, 'order_6': 38, 'order_5': 0, 'mixed_4_6': 61},
            7: {'order_4': 0, 'order_6': 0, 'order_5': 50, 'mixed_4_6': 0},
            8: {'order_4': 32, 'order_6': 38, 'order_5': 0, 'mixed_4_6': 61},
            9: {'order_4': 0, 'order_6': 0, 'order_5': 50, 'mixed_4_6': 0},
            10: {'order_4': 32, 'order_6': 37, 'order_5': 0, 'mixed_4_6': 61},
            11: {'order_4': 0, 'order_6': 0, 'order_5': 50, 'mixed_4_6': 0}
        }
        
        print("Calculateur du nombre total de mains dans les 24 séquences maximales")
    
    def calculate_total_hands(self):
        """Calcule le nombre total de mains dans toutes les séquences"""
        
        print("="*70)
        print("CALCUL DU NOMBRE TOTAL DE MAINS")
        print("="*70)
        
        total_hands = 0
        details_by_burn = {}
        
        for burn_cards in range(2, 12):
            burn_total = 0
            burn_details = {}
            
            print(f"\nBRÛLAGE {burn_cards}:")
            
            # Ordre 4
            if self.sequences_data[burn_cards]['order_4'] > 0:
                hands_4 = self.sequences_data[burn_cards]['order_4'] * self.max_lengths[burn_cards]['order_4']
                burn_total += hands_4
                burn_details['order_4'] = hands_4
                print(f"  Ordre 4: {self.sequences_data[burn_cards]['order_4']} séquence × {self.max_lengths[burn_cards]['order_4']} mains = {hands_4} mains")
            
            # Ordre 6
            if self.sequences_data[burn_cards]['order_6'] > 0:
                hands_6 = self.sequences_data[burn_cards]['order_6'] * self.max_lengths[burn_cards]['order_6']
                burn_total += hands_6
                burn_details['order_6'] = hands_6
                print(f"  Ordre 6: {self.sequences_data[burn_cards]['order_6']} séquence × {self.max_lengths[burn_cards]['order_6']} mains = {hands_6} mains")
            
            # Ordre 5
            if self.sequences_data[burn_cards]['order_5'] > 0:
                hands_5 = self.sequences_data[burn_cards]['order_5'] * self.max_lengths[burn_cards]['order_5']
                burn_total += hands_5
                burn_details['order_5'] = hands_5
                print(f"  Ordre 5: {self.sequences_data[burn_cards]['order_5']} séquence × {self.max_lengths[burn_cards]['order_5']} mains = {hands_5} mains")
            
            # Mixte 4+6
            if self.sequences_data[burn_cards]['mixed_4_6'] > 0:
                hands_mixed = self.sequences_data[burn_cards]['mixed_4_6'] * self.max_lengths[burn_cards]['mixed_4_6']
                burn_total += hands_mixed
                burn_details['mixed_4_6'] = hands_mixed
                print(f"  Mixte 4+6: {self.sequences_data[burn_cards]['mixed_4_6']} séquences × {self.max_lengths[burn_cards]['mixed_4_6']} mains = {hands_mixed} mains")
            
            print(f"  TOTAL BRÛLAGE {burn_cards}: {burn_total} mains")
            
            total_hands += burn_total
            details_by_burn[burn_cards] = {
                'total': burn_total,
                'details': burn_details
            }
        
        print("\n" + "="*70)
        print("RÉSUMÉ PAR TYPE DE SÉQUENCE")
        print("="*70)
        
        # Calculer les totaux par type
        total_order_4 = 0
        total_order_6 = 0
        total_order_5 = 0
        total_mixed = 0
        
        for burn_cards in range(2, 12):
            if self.sequences_data[burn_cards]['order_4'] > 0:
                total_order_4 += self.sequences_data[burn_cards]['order_4'] * self.max_lengths[burn_cards]['order_4']
            
            if self.sequences_data[burn_cards]['order_6'] > 0:
                total_order_6 += self.sequences_data[burn_cards]['order_6'] * self.max_lengths[burn_cards]['order_6']
            
            if self.sequences_data[burn_cards]['order_5'] > 0:
                total_order_5 += self.sequences_data[burn_cards]['order_5'] * self.max_lengths[burn_cards]['order_5']
            
            if self.sequences_data[burn_cards]['mixed_4_6'] > 0:
                total_mixed += self.sequences_data[burn_cards]['mixed_4_6'] * self.max_lengths[burn_cards]['mixed_4_6']
        
        print(f"1. Total mains ORDRE 4 (5 séquences): {total_order_4:,} mains")
        print(f"2. Total mains ORDRE 6 (5 séquences): {total_order_6:,} mains")
        print(f"3. Total mains ORDRE 5 (5 séquences): {total_order_5:,} mains")
        print(f"4. Total mains MIXTES 4+6 (9 séquences): {total_mixed:,} mains")
        
        print(f"\nTOTAL MAINS PUREMENT PAIRES: {total_order_4 + total_order_6 + total_mixed:,}")
        print(f"TOTAL MAINS PUREMENT IMPAIRES: {total_order_5:,}")
        print(f"TOTAL GÉNÉRAL: {total_hands:,} mains")
        
        print("\n" + "="*70)
        print("VÉRIFICATION DES CALCULS")
        print("="*70)
        
        # Vérification détaillée
        sequences_count = 0
        for burn_cards in range(2, 12):
            sequences_count += (self.sequences_data[burn_cards]['order_4'] + 
                              self.sequences_data[burn_cards]['order_6'] + 
                              self.sequences_data[burn_cards]['order_5'] + 
                              self.sequences_data[burn_cards]['mixed_4_6'])
        
        print(f"Nombre total de séquences: {sequences_count}")
        print(f"Nombre total de mains: {total_hands:,}")
        print(f"Moyenne de mains par séquence: {total_hands / sequences_count:.1f}")
        
        return {
            'total_hands': total_hands,
            'by_type': {
                'order_4': total_order_4,
                'order_6': total_order_6,
                'order_5': total_order_5,
                'mixed_4_6': total_mixed
            },
            'by_burn': details_by_burn,
            'sequences_count': sequences_count
        }

if __name__ == "__main__":
    calculator = BaccaratTotalHandsCalculator()
    results = calculator.calculate_total_hands()
