#!/usr/bin/env python3
"""
Énumérateur de configurations Baccarat
Compte toutes les parties distinctes purement paires/impaires possibles
"""

from itertools import product
from collections import defaultdict

class BaccaratConfigurationEnumerator:
    def __init__(self):
        # Contraintes exactes
        self.total_cards = 312
        
        # Brûlages possibles selon les règles
        self.burn_configurations = {
            2: {'card_value': 1, 'is_even': True},    # As → 2 cartes brûlées
            3: {'card_value': 2, 'is_even': False},   # 2 → 3 cartes brûlées
            4: {'card_value': 3, 'is_even': True},    # 3 → 4 cartes brûlées
            5: {'card_value': 4, 'is_even': False},   # 4 → 5 cartes brûlées
            6: {'card_value': 5, 'is_even': True},    # 5 → 6 cartes brûlées
            7: {'card_value': 6, 'is_even': False},   # 6 → 7 cartes brûlées
            8: {'card_value': 7, 'is_even': True},    # 7 → 8 cartes brûlées
            9: {'card_value': 8, 'is_even': False},   # 8 → 9 cartes brûlées
            10: {'card_value': 9, 'is_even': False},  # 9 → 10 cartes brûlées
            11: {'card_value': 0, 'is_even': False}   # 10/J/Q/K → 11 cartes brûlées
        }
        
        # Cartes possibles par main selon les règles officielles
        self.possible_hand_cards = [4, 5, 6]
        
        print("Énumérateur de configurations Baccarat")
        print(f"Total cartes par partie: {self.total_cards}")
        print(f"Brûlages possibles: {list(self.burn_configurations.keys())}")
        print(f"Cartes par main: {self.possible_hand_cards}")
    
    def find_all_hand_combinations(self, remaining_cards):
        """Trouve toutes les combinaisons possibles de mains pour les cartes restantes"""
        valid_combinations = []
        
        # Limites pratiques pour éviter les calculs trop longs
        max_hands = min(remaining_cards // 4, 100)  # Maximum 100 mains
        
        # Énumération systématique
        for hands_4 in range(max_hands + 1):
            cards_used_4 = hands_4 * 4
            if cards_used_4 > remaining_cards:
                break
                
            remaining_after_4 = remaining_cards - cards_used_4
            max_hands_5 = min(remaining_after_4 // 5, 100)
            
            for hands_5 in range(max_hands_5 + 1):
                cards_used_5 = hands_5 * 5
                remaining_after_5 = remaining_after_4 - cards_used_5
                
                if remaining_after_5 < 0:
                    break
                
                # Le reste doit être exactement divisible par 6
                if remaining_after_5 % 6 == 0:
                    hands_6 = remaining_after_5 // 6
                    total_hands = hands_4 + hands_5 + hands_6
                    
                    if total_hands > 0:  # Au moins une main
                        combination = {
                            'hands_4': hands_4,
                            'hands_5': hands_5,
                            'hands_6': hands_6,
                            'total_hands': total_hands,
                            'total_cards_used': cards_used_4 + cards_used_5 + (hands_6 * 6)
                        }
                        
                        # Vérification
                        if combination['total_cards_used'] == remaining_cards:
                            valid_combinations.append(combination)
        
        return valid_combinations
    
    def is_purely_even_configuration(self, burn_cards, hand_combination):
        """Vérifie si une configuration est purement paire"""
        # Brûlage doit être pair
        if not self.burn_configurations[burn_cards]['is_even']:
            return False
        
        # Toutes les mains doivent être paires (4 ou 6 cartes)
        if hand_combination['hands_5'] > 0:  # Aucune main de 5 cartes
            return False
        
        return True
    
    def is_purely_odd_configuration(self, burn_cards, hand_combination):
        """Vérifie si une configuration est purement impaire"""
        # Brûlage doit être impair
        if self.burn_configurations[burn_cards]['is_even']:
            return False
        
        # Toutes les mains doivent être impaires (5 cartes)
        if hand_combination['hands_4'] > 0 or hand_combination['hands_6'] > 0:
            return False
        
        return True
    
    def is_purely_even_pair4_only(self, burn_cards, hand_combination):
        """Vérifie si une configuration est paire avec pair_4 uniquement"""
        # Brûlage doit être pair
        if not self.burn_configurations[burn_cards]['is_even']:
            return False
        
        # Toutes les mains doivent être de 4 cartes
        if hand_combination['hands_5'] > 0 or hand_combination['hands_6'] > 0:
            return False
        
        return True
    
    def is_purely_even_pair6_only(self, burn_cards, hand_combination):
        """Vérifie si une configuration est paire avec pair_6 uniquement"""
        # Brûlage doit être pair
        if not self.burn_configurations[burn_cards]['is_even']:
            return False
        
        # Toutes les mains doivent être de 6 cartes
        if hand_combination['hands_4'] > 0 or hand_combination['hands_5'] > 0:
            return False
        
        return True
    
    def enumerate_all_configurations(self):
        """Énumère toutes les configurations possibles"""
        results = {
            'purely_even': [],
            'purely_odd': [],
            'purely_even_pair4': [],
            'purely_even_pair6': [],
            'total_configurations': 0
        }
        
        print("\n" + "="*70)
        print("ÉNUMÉRATION DE TOUTES LES CONFIGURATIONS")
        print("="*70)
        
        for burn_cards in self.burn_configurations.keys():
            remaining_cards = self.total_cards - burn_cards
            
            print(f"\nBrûlage {burn_cards} cartes (pair: {self.burn_configurations[burn_cards]['is_even']})")
            print(f"Cartes restantes: {remaining_cards}")
            
            # Trouver toutes les combinaisons de mains possibles
            hand_combinations = self.find_all_hand_combinations(remaining_cards)
            
            print(f"Combinaisons de mains trouvées: {len(hand_combinations)}")
            
            # Analyser chaque combinaison
            burn_purely_even = 0
            burn_purely_odd = 0
            burn_purely_pair4 = 0
            burn_purely_pair6 = 0
            
            for combination in hand_combinations:
                results['total_configurations'] += 1
                
                # Configuration complète
                config = {
                    'burn_cards': burn_cards,
                    'burn_is_even': self.burn_configurations[burn_cards]['is_even'],
                    'hands_4': combination['hands_4'],
                    'hands_5': combination['hands_5'],
                    'hands_6': combination['hands_6'],
                    'total_hands': combination['total_hands']
                }
                
                # Vérifier les conditions
                if self.is_purely_even_configuration(burn_cards, combination):
                    results['purely_even'].append(config)
                    burn_purely_even += 1
                
                if self.is_purely_odd_configuration(burn_cards, combination):
                    results['purely_odd'].append(config)
                    burn_purely_odd += 1
                
                if self.is_purely_even_pair4_only(burn_cards, combination):
                    results['purely_even_pair4'].append(config)
                    burn_purely_pair4 += 1
                
                if self.is_purely_even_pair6_only(burn_cards, combination):
                    results['purely_even_pair6'].append(config)
                    burn_purely_pair6 += 1
            
            print(f"  Purement paires: {burn_purely_even}")
            print(f"  Purement impaires: {burn_purely_odd}")
            print(f"  Pair_4 uniquement: {burn_purely_pair4}")
            print(f"  Pair_6 uniquement: {burn_purely_pair6}")
        
        return results
    
    def display_results(self, results):
        """Affiche les résultats détaillés"""
        print("\n" + "="*70)
        print("RÉSULTATS FINAUX - CONFIGURATIONS DISTINCTES")
        print("="*70)
        
        print(f"Total configurations analysées: {results['total_configurations']:,}")
        print()
        
        print(f"1. Parties purement paires: {len(results['purely_even']):,}")
        print(f"2. Parties purement impaires: {len(results['purely_odd']):,}")
        print(f"3. Parties paires avec pair_4 uniquement: {len(results['purely_even_pair4']):,}")
        print(f"4. Parties paires avec pair_6 uniquement: {len(results['purely_even_pair6']):,}")
        
        total_pure = len(results['purely_even']) + len(results['purely_odd'])
        print(f"\nTotal parties pures: {total_pure:,}")
        
        # Afficher quelques exemples
        if results['purely_even']:
            print(f"\nExemples de parties purement paires:")
            for i, config in enumerate(results['purely_even'][:3]):
                print(f"  {i+1}. Brûlage {config['burn_cards']} + {config['hands_4']} mains de 4 + {config['hands_6']} mains de 6")
        
        if results['purely_odd']:
            print(f"\nExemples de parties purement impaires:")
            for i, config in enumerate(results['purely_odd'][:3]):
                print(f"  {i+1}. Brûlage {config['burn_cards']} + {config['hands_5']} mains de 5")
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE")
        print("="*70)
        print("✓ Énumération exhaustive de toutes les configurations")
        print("✓ Respect strict des contraintes (312 cartes)")
        print("✓ Application des règles de brûlage officielles")
        print("✓ Classification selon les critères pair/impair")
        print("✓ Comptage des configurations distinctes (pas d'arrangements)")
    
    def run_enumeration(self):
        """Exécute l'énumération complète"""
        print("="*70)
        print("ÉNUMÉRATEUR DE CONFIGURATIONS BACCARAT")
        print("Comptage des parties distinctes purement paires/impaires")
        print("="*70)
        
        results = self.enumerate_all_configurations()
        self.display_results(results)
        
        return results

if __name__ == "__main__":
    enumerator = BaccaratConfigurationEnumerator()
    final_results = enumerator.run_enumeration()
