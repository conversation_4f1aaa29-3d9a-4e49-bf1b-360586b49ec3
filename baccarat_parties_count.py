#!/usr/bin/env python3
"""
Calculateur du nombre de parties possibles au Baccarat
Compte les parties distinctes, pas les arrangements
"""

class BaccaratPartiesCounter:
    def __init__(self):
        # Contraintes exactes
        self.total_cards = 312  # Cut card à la 312ème carte
        
        # Brûlage possible : 2 à 11 cartes
        self.burn_range = list(range(2, 12))
        self.even_burns = [2, 4, 6, 8, 10]  # Brûlages pairs
        self.odd_burns = [3, 5, 7, 9, 11]   # Brûlages impairs
        
        # Cartes par main selon les règles officielles
        self.even_hands = [4, 6]     # Mains paires
        self.odd_hands = [5]         # Mains impaires
        
        print("Calcul du nombre de parties possibles")
        print(f"Total cartes: {self.total_cards}")
        print(f"Brûlages pairs: {self.even_burns}")
        print(f"Brûlages impairs: {self.odd_burns}")
        print(f"Mains paires: {self.even_hands}")
        print(f"Mains impaires: {self.odd_hands}")
    
    def find_valid_hand_combinations(self, remaining_cards):
        """Trouve toutes les combinaisons valides de mains pour les cartes restantes"""
        valid_combinations = []
        
        # Essayer toutes les combinaisons possibles de mains 4, 5, 6 cartes
        max_hands_4 = remaining_cards // 4
        
        for hands_4 in range(max_hands_4 + 1):
            remaining_after_4 = remaining_cards - (hands_4 * 4)
            max_hands_5 = remaining_after_4 // 5
            
            for hands_5 in range(max_hands_5 + 1):
                remaining_after_5 = remaining_after_4 - (hands_5 * 5)
                
                if remaining_after_5 % 6 == 0:  # Le reste doit être divisible par 6
                    hands_6 = remaining_after_5 // 6
                    total_hands = hands_4 + hands_5 + hands_6
                    
                    if total_hands > 0:  # Au moins une main
                        valid_combinations.append({
                            'hands_4': hands_4,
                            'hands_5': hands_5,
                            'hands_6': hands_6,
                            'total_hands': total_hands,
                            'total_cards': hands_4 * 4 + hands_5 * 5 + hands_6 * 6
                        })
        
        return valid_combinations
    
    def count_pure_even_parties(self):
        """Compte le nombre de parties purement paires possibles"""
        total_parties = 0
        details = []
        
        print("\n" + "="*60)
        print("PARTIES PUREMENT PAIRES")
        print("="*60)
        print("Contrainte: Brûlage pair + toutes les mains paires (4 ou 6 cartes)")
        
        for burn_cards in self.even_burns:
            remaining_cards = self.total_cards - burn_cards
            print(f"\nBrûlage {burn_cards} cartes, reste {remaining_cards} cartes:")
            
            # Trouver toutes les combinaisons valides
            valid_combinations = self.find_valid_hand_combinations(remaining_cards)
            
            # Filtrer pour ne garder que les combinaisons purement paires
            pure_even_combinations = [
                combo for combo in valid_combinations 
                if combo['hands_5'] == 0  # Aucune main impaire (5 cartes)
            ]
            
            burn_count = len(pure_even_combinations)
            total_parties += burn_count
            
            print(f"  Nombre de parties possibles: {burn_count}")
            
            # Afficher quelques exemples
            for i, combo in enumerate(pure_even_combinations[:5]):  # Afficher les 5 premières
                print(f"    Partie {i+1}: {combo['hands_4']} mains de 4 + {combo['hands_6']} mains de 6")
            
            if len(pure_even_combinations) > 5:
                print(f"    ... et {len(pure_even_combinations) - 5} autres parties")
            
            details.append({
                'burn_cards': burn_cards,
                'parties_count': burn_count,
                'combinations': pure_even_combinations
            })
        
        return total_parties, details
    
    def count_pure_odd_parties(self):
        """Compte le nombre de parties purement impaires possibles"""
        total_parties = 0
        details = []
        
        print("\n" + "="*60)
        print("PARTIES PUREMENT IMPAIRES")
        print("="*60)
        print("Contrainte: Brûlage impair + toutes les mains impaires (5 cartes)")
        
        for burn_cards in self.odd_burns:
            remaining_cards = self.total_cards - burn_cards
            print(f"\nBrûlage {burn_cards} cartes, reste {remaining_cards} cartes:")
            
            # Trouver toutes les combinaisons valides
            valid_combinations = self.find_valid_hand_combinations(remaining_cards)
            
            # Filtrer pour ne garder que les combinaisons purement impaires
            pure_odd_combinations = [
                combo for combo in valid_combinations 
                if combo['hands_4'] == 0 and combo['hands_6'] == 0  # Que des mains de 5 cartes
            ]
            
            burn_count = len(pure_odd_combinations)
            total_parties += burn_count
            
            print(f"  Nombre de parties possibles: {burn_count}")
            
            # Afficher les exemples
            for i, combo in enumerate(pure_odd_combinations):
                print(f"    Partie {i+1}: {combo['hands_5']} mains de 5")
            
            details.append({
                'burn_cards': burn_cards,
                'parties_count': burn_count,
                'combinations': pure_odd_combinations
            })
        
        return total_parties, details
    
    def count_pure_even_pair4_only(self):
        """Compte les parties paires avec pair_4 uniquement"""
        total_parties = 0
        details = []
        
        print("\n" + "="*60)
        print("PARTIES PAIR_4 UNIQUEMENT")
        print("="*60)
        
        for burn_cards in self.even_burns:
            remaining_cards = self.total_cards - burn_cards
            print(f"Brûlage {burn_cards} cartes, reste {remaining_cards} cartes:")
            
            # Vérifier si on peut avoir que des mains de 4 cartes
            if remaining_cards % 4 == 0:
                hands_4 = remaining_cards // 4
                total_parties += 1
                print(f"  1 partie possible: {hands_4} mains de 4")
                details.append({
                    'burn_cards': burn_cards,
                    'hands_4': hands_4,
                    'possible': True
                })
            else:
                print(f"  Impossible (reste {remaining_cards % 4} cartes)")
                details.append({
                    'burn_cards': burn_cards,
                    'possible': False
                })
        
        return total_parties, details
    
    def count_pure_even_pair6_only(self):
        """Compte les parties paires avec pair_6 uniquement"""
        total_parties = 0
        details = []
        
        print("\n" + "="*60)
        print("PARTIES PAIR_6 UNIQUEMENT")
        print("="*60)
        
        for burn_cards in self.even_burns:
            remaining_cards = self.total_cards - burn_cards
            print(f"Brûlage {burn_cards} cartes, reste {remaining_cards} cartes:")
            
            # Vérifier si on peut avoir que des mains de 6 cartes
            if remaining_cards % 6 == 0:
                hands_6 = remaining_cards // 6
                total_parties += 1
                print(f"  1 partie possible: {hands_6} mains de 6")
                details.append({
                    'burn_cards': burn_cards,
                    'hands_6': hands_6,
                    'possible': True
                })
            else:
                print(f"  Impossible (reste {remaining_cards % 6} cartes)")
                details.append({
                    'burn_cards': burn_cards,
                    'possible': False
                })
        
        return total_parties, details
    
    def run_parties_count(self):
        """Exécute le comptage des parties"""
        print("="*70)
        print("COMPTAGE DES PARTIES BACCARAT POSSIBLES")
        print("Nombre de parties distinctes, pas d'arrangements")
        print("="*70)
        
        # Comptages
        pure_even, even_details = self.count_pure_even_parties()
        pure_odd, odd_details = self.count_pure_odd_parties()
        pure_pair4, pair4_details = self.count_pure_even_pair4_only()
        pure_pair6, pair6_details = self.count_pure_even_pair6_only()
        
        print("\n" + "="*70)
        print("RÉSULTATS FINAUX")
        print("="*70)
        print(f"1. Parties purement paires: {pure_even}")
        print(f"2. Parties purement impaires: {pure_odd}")
        print(f"3. Parties paires avec pair_4 uniquement: {pure_pair4}")
        print(f"4. Parties paires avec pair_6 uniquement: {pure_pair6}")
        
        total_pure = pure_even + pure_odd
        print(f"\nTotal parties pures (paires + impaires): {total_pure}")
        
        print("\n" + "="*70)
        print("EXPLICATION")
        print("="*70)
        print("Chaque 'partie' représente une configuration unique de:")
        print("- Un type de brûlage (2-11 cartes)")
        print("- Une combinaison spécifique de mains (4, 5, 6 cartes)")
        print("- Qui respecte les contraintes paires/impaires")
        print("- Et utilise exactement 312 cartes")
        
        return {
            'pure_even': pure_even,
            'pure_odd': pure_odd,
            'pure_pair4': pure_pair4,
            'pure_pair6': pure_pair6,
            'total_pure': total_pure,
            'details': {
                'even': even_details,
                'odd': odd_details,
                'pair4': pair4_details,
                'pair6': pair6_details
            }
        }

if __name__ == "__main__":
    counter = BaccaratPartiesCounter()
    results = counter.run_parties_count()
