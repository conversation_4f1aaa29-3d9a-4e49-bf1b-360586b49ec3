#!/usr/bin/env python3
"""
Tableau complet des probabilités par type de brûlage et longueur
Regroupe toutes les probabilités pour chaque configuration
"""

import math

class BaccaratCompleteProbabilityTable:
    def __init__(self):
        self.total_cards = 312
        
        # Probabilités officielles basées sur les règles du Baccarat (8 decks)
        self.hand_probabilities = {
            'natural_4_cards': 0.458597,      # Naturels + aucune 3ème carte
            'single_draw_5_cards': 0.446247,  # Un seul joueur tire
            'double_draw_6_cards': 0.095156,  # Les deux joueurs tirent
            'mixed_4_6_avg': 0.276876         # Moyenne pour séquences mixtes
        }
        
        # Longueurs maximales par brûlage
        self.max_lengths = {
            2: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            3: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            4: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            5: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            6: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            7: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            8: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            9: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            10: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            11: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0}
        }
        
        # Longueurs clés à analyser
        self.key_lengths = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60]
        
        print("Générateur du tableau complet des probabilités")
        print("Par type de brûlage et longueur de séquence")
    
    def calculate_probability(self, base_prob, length):
        """Calcule la probabilité d'une séquence de longueur donnée"""
        if length <= 0:
            return 0
        return base_prob ** length
    
    def format_probability(self, prob):
        """Formate une probabilité pour affichage dans le tableau"""
        if prob == 0:
            return "-"
        elif prob >= 1e-2:
            return f"{prob:.4f}"
        elif prob >= 1e-6:
            return f"{prob:.2e}"
        else:
            return f"{prob:.1e}"
    
    def generate_complete_table(self):
        """Génère le tableau complet des probabilités"""
        
        print("="*120)
        print("TABLEAU COMPLET DES PROBABILITÉS PAR BRÛLAGE ET LONGUEUR")
        print("="*120)
        
        # En-tête du tableau
        print("\nBRÛLAGES PAIRS (Séquences purement paires)")
        print("="*120)
        
        # Tableau pour brûlages pairs
        self.generate_even_burns_table()
        
        print("\nBRÛLAGES IMPAIRS (Séquences purement impaires)")
        print("="*120)
        
        # Tableau pour brûlages impairs
        self.generate_odd_burns_table()
        
        # Tableau de synthèse
        print("\nTABLEAU DE SYNTHÈSE - LONGUEURS PRATIQUES")
        print("="*120)
        self.generate_practical_summary()
    
    def generate_even_burns_table(self):
        """Génère le tableau pour les brûlages pairs"""
        
        even_burns = [2, 4, 6, 8, 10]
        
        # En-tête
        print(f"{'Longueur':>8} | {'Brûlage 2':>25} | {'Brûlage 4':>25} | {'Brûlage 6':>25} | {'Brûlage 8':>25} | {'Brûlage 10':>25}")
        print(f"{'':>8} | {'O4':>7} {'O6':>7} {'Mix':>7} | {'O4':>7} {'O6':>7} {'Mix':>7} | {'O4':>7} {'O6':>7} {'Mix':>7} | {'O4':>7} {'O6':>7} {'Mix':>7} | {'O4':>7} {'O6':>7} {'Mix':>7}")
        print("-" * 140)
        
        # Calculer les probabilités pour chaque longueur
        for length in self.key_lengths:
            if length > 60:  # Limite raisonnable pour l'affichage
                continue
                
            row = f"{length:>8} |"
            
            for burn in even_burns:
                max_data = self.max_lengths[burn]
                
                # Ordre 4
                if max_data['order_4'] > 0 and length <= max_data['order_4']:
                    prob_4 = self.calculate_probability(self.hand_probabilities['natural_4_cards'], length)
                    prob_4_str = self.format_probability(prob_4)
                else:
                    prob_4_str = "-"
                
                # Ordre 6
                if max_data['order_6'] > 0 and length <= max_data['order_6']:
                    prob_6 = self.calculate_probability(self.hand_probabilities['double_draw_6_cards'], length)
                    prob_6_str = self.format_probability(prob_6)
                else:
                    prob_6_str = "-"
                
                # Mixte 4+6
                if max_data['mixed_4_6'] > 0 and length <= max_data['mixed_4_6']:
                    prob_mix = self.calculate_probability(self.hand_probabilities['mixed_4_6_avg'], length)
                    prob_mix_str = self.format_probability(prob_mix)
                else:
                    prob_mix_str = "-"
                
                row += f" {prob_4_str:>7} {prob_6_str:>7} {prob_mix_str:>7} |"
            
            print(row)
    
    def generate_odd_burns_table(self):
        """Génère le tableau pour les brûlages impairs"""
        
        odd_burns = [3, 5, 7, 9, 11]
        
        # En-tête
        print(f"{'Longueur':>8} | {'Brûlage 3':>10} | {'Brûlage 5':>10} | {'Brûlage 7':>10} | {'Brûlage 9':>10} | {'Brûlage 11':>10}")
        print(f"{'':>8} | {'O5':>10} | {'O5':>10} | {'O5':>10} | {'O5':>10} | {'O5':>10}")
        print("-" * 70)
        
        # Calculer les probabilités pour chaque longueur
        for length in self.key_lengths:
            if length > 60:  # Limite raisonnable pour l'affichage
                continue
                
            row = f"{length:>8} |"
            
            for burn in odd_burns:
                max_data = self.max_lengths[burn]
                
                # Ordre 5
                if max_data['order_5'] > 0 and length <= max_data['order_5']:
                    prob_5 = self.calculate_probability(self.hand_probabilities['single_draw_5_cards'], length)
                    prob_5_str = self.format_probability(prob_5)
                else:
                    prob_5_str = "-"
                
                row += f" {prob_5_str:>10} |"
            
            print(row)
    
    def generate_practical_summary(self):
        """Génère un tableau de synthèse des longueurs pratiques"""
        
        # Seuils pratiques
        thresholds = {
            'Très Probable (>10%)': 1e-1,
            'Probable (>1%)': 1e-2,
            'Possible (>0.1%)': 1e-3,
            'Rare (>0.0001%)': 1e-6,
            'Très Rare (>1e-9)': 1e-9,
            'Quasi Impossible': 1e-12
        }
        
        print(f"{'Seuil':>20} | {'Ordre 4':>8} | {'Ordre 6':>8} | {'Ordre 5':>8} | {'Mixte 4+6':>10}")
        print("-" * 60)
        
        for name, threshold in thresholds.items():
            # Calculer longueur max pour chaque type
            max_4 = int(math.log(threshold) / math.log(self.hand_probabilities['natural_4_cards'])) if threshold > 0 else 0
            max_6 = int(math.log(threshold) / math.log(self.hand_probabilities['double_draw_6_cards'])) if threshold > 0 else 0
            max_5 = int(math.log(threshold) / math.log(self.hand_probabilities['single_draw_5_cards'])) if threshold > 0 else 0
            max_mix = int(math.log(threshold) / math.log(self.hand_probabilities['mixed_4_6_avg'])) if threshold > 0 else 0
            
            print(f"{name:>20} | {max_4:>8} | {max_6:>8} | {max_5:>8} | {max_mix:>10}")
    
    def generate_detailed_analysis(self):
        """Génère une analyse détaillée par brûlage"""
        
        print("\n" + "="*120)
        print("ANALYSE DÉTAILLÉE PAR BRÛLAGE")
        print("="*120)
        
        for burn_cards in range(2, 12):
            remaining_cards = self.total_cards - burn_cards
            burn_is_even = (burn_cards % 2 == 0)
            max_data = self.max_lengths[burn_cards]
            
            print(f"\nBRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'}) - {remaining_cards} cartes restantes")
            print("-" * 80)
            
            if burn_is_even:
                print("Longueurs maximales théoriques:")
                if max_data['order_4'] > 0:
                    print(f"- Ordre 4 (naturels): {max_data['order_4']} mains")
                if max_data['order_6'] > 0:
                    print(f"- Ordre 6 (doubles tirages): {max_data['order_6']} mains")
                if max_data['mixed_4_6'] > 0:
                    print(f"- Mixte 4+6: {max_data['mixed_4_6']} mains")
                
                print("\nLongueurs pratiques (probabilité > 1%):")
                
                # Ordre 4
                if max_data['order_4'] > 0:
                    practical_4 = int(math.log(0.01) / math.log(self.hand_probabilities['natural_4_cards']))
                    print(f"- Ordre 4: jusqu'à {min(practical_4, max_data['order_4'])} mains")
                
                # Ordre 6
                if max_data['order_6'] > 0:
                    practical_6 = int(math.log(0.01) / math.log(self.hand_probabilities['double_draw_6_cards']))
                    print(f"- Ordre 6: jusqu'à {min(practical_6, max_data['order_6'])} mains")
                
                # Mixte
                if max_data['mixed_4_6'] > 0:
                    practical_mix = int(math.log(0.01) / math.log(self.hand_probabilities['mixed_4_6_avg']))
                    print(f"- Mixte 4+6: jusqu'à {min(practical_mix, max_data['mixed_4_6'])} mains")
            
            else:
                print("Longueur maximale théorique:")
                if max_data['order_5'] > 0:
                    print(f"- Ordre 5 (tirages uniques): {max_data['order_5']} mains")
                
                print("\nLongueur pratique (probabilité > 1%):")
                if max_data['order_5'] > 0:
                    practical_5 = int(math.log(0.01) / math.log(self.hand_probabilities['single_draw_5_cards']))
                    print(f"- Ordre 5: jusqu'à {min(practical_5, max_data['order_5'])} mains")
    
    def run_complete_analysis(self):
        """Exécute l'analyse complète"""
        
        print("="*120)
        print("GÉNÉRATEUR DU TABLEAU COMPLET DES PROBABILITÉS")
        print("="*120)
        
        print("\nLégende:")
        print("- O4: Ordre 4 (naturels)")
        print("- O6: Ordre 6 (doubles tirages)")
        print("- O5: Ordre 5 (tirages uniques)")
        print("- Mix: Mixte 4+6 (séquences combinées)")
        print("- Les valeurs sont les probabilités de réaliser la longueur donnée")
        
        # Générer le tableau principal
        self.generate_complete_table()
        
        # Générer l'analyse détaillée
        self.generate_detailed_analysis()
        
        print("\n" + "="*120)
        print("NOTES IMPORTANTES")
        print("="*120)
        print("1. Les probabilités diminuent exponentiellement avec la longueur")
        print("2. Les séquences > 10 mains sont très rares en pratique")
        print("3. Les longueurs maximales sont purement théoriques")
        print("4. Les brûlages pairs permettent 3 types de séquences")
        print("5. Les brûlages impairs ne permettent qu'un seul type")

if __name__ == "__main__":
    calculator = BaccaratCompleteProbabilityTable()
    calculator.run_complete_analysis()
