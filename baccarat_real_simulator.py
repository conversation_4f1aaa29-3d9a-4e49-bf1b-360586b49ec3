#!/usr/bin/env python3
"""
Simulateur réel du Baccarat avec les vraies règles
Applique les règles de tirage selon les valeurs des cartes
"""

import random
from collections import defaultdict

class BaccaratRealSimulator:
    def __init__(self):
        # Configuration selon regles_baccarat_essentielles.txt
        self.total_cards = 312  # Cut card à la 312ème carte
        self.deck_size = 416    # 8 jeux de 52 cartes
        
        # Création du deck complet (8 jeux)
        self.full_deck = self.create_full_deck()
        
        # Valeurs Baccarat
        self.card_values = {
            'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
            '10': 0, 'J': 0, 'Q': 0, 'K': 0
        }
        
        print("Simulateur Baccarat réel initialisé")
        print(f"Deck complet: {len(self.full_deck)} cartes")
        print(f"Cartes utilisées: {self.total_cards}")
    
    def create_full_deck(self):
        """Crée un deck complet de 8 jeux de 52 cartes"""
        suits = ['♠', '♥', '♦', '♣']
        ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
        
        deck = []
        for _ in range(8):  # 8 jeux
            for suit in suits:
                for rank in ranks:
                    deck.append(rank)  # On garde seulement la valeur pour simplifier
        
        return deck
    
    def get_baccarat_value(self, card):
        """Retourne la valeur Baccarat d'une carte"""
        return self.card_values[card]
    
    def calculate_hand_total(self, cards):
        """Calcule le total d'une main (modulo 10)"""
        total = sum(self.get_baccarat_value(card) for card in cards)
        return total % 10
    
    def apply_burn_rules(self, deck):
        """Applique les règles de brûlage selon la première carte"""
        if len(deck) == 0:
            return [], 0
        
        # Tirer la première carte
        burn_card = deck[0]
        burn_value = self.get_baccarat_value(burn_card)
        
        # Calculer le nombre de cartes à brûler
        if burn_card in ['10', 'J', 'Q', 'K']:
            cards_to_burn = 11  # 1 + 10 supplémentaires
        else:
            cards_to_burn = 1 + burn_value  # 1 + valeur de la carte
        
        # Brûler les cartes
        burned_cards = deck[:cards_to_burn]
        remaining_deck = deck[cards_to_burn:]
        
        return remaining_deck, cards_to_burn
    
    def should_player_draw(self, player_total):
        """Détermine si le joueur doit tirer selon les règles"""
        if player_total in [8, 9]:  # Naturel
            return False
        if player_total in [6, 7]:  # Reste
            return False
        return True  # 0-5 tire
    
    def should_banker_draw(self, banker_total, player_total, player_third_card_value=None):
        """Détermine si le banquier doit tirer selon les règles du tableau"""
        if banker_total in [8, 9]:  # Naturel
            return False
        
        # Si le joueur n'a pas tiré (reste avec 6 ou 7)
        if player_third_card_value is None:
            return banker_total <= 5
        
        # Règles selon le tableau officiel
        if banker_total == 7:
            return False
        elif banker_total == 6:
            return player_third_card_value in [6, 7]
        elif banker_total == 5:
            return player_third_card_value in [4, 5, 6, 7]
        elif banker_total == 4:
            return player_third_card_value in [2, 3, 4, 5, 6, 7]
        elif banker_total == 3:
            return player_third_card_value != 8
        else:  # 0, 1, 2
            return True
    
    def play_hand(self, deck, start_index):
        """Joue une main complète selon les vraies règles"""
        if start_index + 4 > len(deck):
            return None, start_index  # Pas assez de cartes
        
        # Distribution initiale : 2 cartes chacun
        player_cards = [deck[start_index], deck[start_index + 2]]
        banker_cards = [deck[start_index + 1], deck[start_index + 3]]
        
        player_total = self.calculate_hand_total(player_cards)
        banker_total = self.calculate_hand_total(banker_cards)
        
        cards_used = 4
        current_index = start_index + 4
        player_third_card_value = None
        
        # Vérification des naturels
        if player_total in [8, 9] or banker_total in [8, 9]:
            winner = 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
            return {
                'cards_used': cards_used,
                'player_total': player_total,
                'banker_total': banker_total,
                'winner': winner
            }, current_index
        
        # Tirage du joueur
        if self.should_player_draw(player_total):
            if current_index < len(deck):
                player_third_card = deck[current_index]
                player_third_card_value = self.get_baccarat_value(player_third_card)
                player_cards.append(player_third_card)
                player_total = self.calculate_hand_total(player_cards)
                cards_used += 1
                current_index += 1
        
        # Tirage du banquier
        if self.should_banker_draw(banker_total, player_total, player_third_card_value):
            if current_index < len(deck):
                banker_third_card = deck[current_index]
                banker_cards.append(banker_third_card)
                banker_total = self.calculate_hand_total(banker_cards)
                cards_used += 1
                current_index += 1
        
        winner = 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
        
        return {
            'cards_used': cards_used,
            'player_total': player_total,
            'banker_total': banker_total,
            'winner': winner
        }, current_index
    
    def simulate_game(self, deck):
        """Simule une partie complète"""
        # Appliquer le brûlage
        remaining_deck, burn_cards = self.apply_burn_rules(deck)
        
        if len(remaining_deck) < 4:
            return None  # Pas assez de cartes pour jouer
        
        game_result = {
            'burn_cards': burn_cards,
            'burn_is_even': burn_cards % 2 == 0,
            'hands': [],
            'all_hands_even': True,
            'all_hands_odd': True
        }
        
        current_index = 0
        
        # Jouer les mains jusqu'à épuisement des cartes ou cut card
        while current_index < len(remaining_deck) - 3:  # Au moins 4 cartes nécessaires
            hand_result, new_index = self.play_hand(remaining_deck, current_index)
            
            if hand_result is None:
                break
            
            game_result['hands'].append(hand_result)
            
            # Vérifier si la main est paire ou impaire
            hand_is_even = hand_result['cards_used'] % 2 == 0
            if not hand_is_even:
                game_result['all_hands_even'] = False
            if hand_is_even:
                game_result['all_hands_odd'] = False
            
            current_index = new_index
        
        return game_result
    
    def count_pure_parties(self, num_simulations=10000):
        """Compte les parties pures par simulation"""
        pure_even_count = 0
        pure_odd_count = 0
        pure_even_pair4_count = 0
        pure_even_pair6_count = 0
        
        print(f"\nSimulation de {num_simulations} parties...")
        
        for i in range(num_simulations):
            if i % 1000 == 0:
                print(f"Progression: {i}/{num_simulations}")
            
            # Mélanger le deck
            deck = self.full_deck.copy()
            random.shuffle(deck)
            
            # Prendre les 312 premières cartes
            game_deck = deck[:self.total_cards]
            
            # Simuler la partie
            result = self.simulate_game(game_deck)
            
            if result is None:
                continue
            
            # Vérifier les conditions
            if result['burn_is_even'] and result['all_hands_even']:
                pure_even_count += 1
                
                # Vérifier si toutes les mains ont 4 cartes
                all_hands_4 = all(hand['cards_used'] == 4 for hand in result['hands'])
                if all_hands_4:
                    pure_even_pair4_count += 1
                
                # Vérifier si toutes les mains ont 6 cartes
                all_hands_6 = all(hand['cards_used'] == 6 for hand in result['hands'])
                if all_hands_6:
                    pure_even_pair6_count += 1
            
            if not result['burn_is_even'] and result['all_hands_odd']:
                pure_odd_count += 1
        
        return {
            'pure_even': pure_even_count,
            'pure_odd': pure_odd_count,
            'pure_even_pair4': pure_even_pair4_count,
            'pure_even_pair6': pure_even_pair6_count,
            'total_simulations': num_simulations
        }
    
    def run_simulation(self):
        """Exécute la simulation complète"""
        print("="*70)
        print("SIMULATEUR BACCARAT RÉEL")
        print("Application des vraies règles selon regles_baccarat_essentielles.txt")
        print("="*70)
        
        # Simulation avec plus d'échantillons
        results = self.count_pure_parties(100000)
        
        print("\n" + "="*70)
        print("RÉSULTATS DE LA SIMULATION")
        print("="*70)
        print(f"Simulations effectuées: {results['total_simulations']:,}")
        print(f"Parties purement paires: {results['pure_even']:,}")
        print(f"Parties purement impaires: {results['pure_odd']:,}")
        print(f"Parties paires avec pair_4 uniquement: {results['pure_even_pair4']:,}")
        print(f"Parties paires avec pair_6 uniquement: {results['pure_even_pair6']:,}")
        
        # Estimation des probabilités
        if results['total_simulations'] > 0:
            prob_even = results['pure_even'] / results['total_simulations']
            prob_odd = results['pure_odd'] / results['total_simulations']
            
            print(f"\nProbabilités estimées:")
            print(f"Parties purement paires: {prob_even:.6f} ({prob_even*100:.4f}%)")
            print(f"Parties purement impaires: {prob_odd:.6f} ({prob_odd*100:.4f}%)")
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE")
        print("="*70)
        print("✓ Vraies règles de brûlage selon la première carte")
        print("✓ Tableau de tirage officiel du Baccarat")
        print("✓ Valeurs réelles des cartes (A=1, 10/J/Q/K=0, etc.)")
        print("✓ Simulation Monte Carlo avec mélange aléatoire")
        print("✓ 312 cartes par partie (cut card)")
        
        return results

if __name__ == "__main__":
    simulator = BaccaratRealSimulator()
    results = simulator.run_simulation()
