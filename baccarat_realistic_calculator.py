#!/usr/bin/env python3
"""
Calculateur réaliste des combinaisons Baccarat
Basé sur la distribution réelle des cartes dans 312 cartes
"""

import math
from fractions import Fraction
from collections import defaultdict

class RealisticBaccaratCalculator:
    def __init__(self):
        # Distribution réaliste dans 312 cartes
        self.total_cards = 312
        
        # Distribution attendue par valeur Baccarat
        self.card_distribution = {
            0: 96,  # 10, J, Q, K (4 types × 24 cartes)
            1: 24,  # As
            2: 24,  # 2
            3: 24,  # 3
            4: 24,  # 4
            5: 24,  # 5
            6: 24,  # 6
            7: 24,  # 7
            8: 24,  # 8
            9: 24   # 9
        }
        
        # Vérification
        total_check = sum(self.card_distribution.values())
        print(f"Vérification distribution: {total_check} cartes")
        
        # Probabilités de brûlage basées sur la distribution réelle
        self.burn_probabilities = {}
        for value, count in self.card_distribution.items():
            prob = Fraction(count, self.total_cards)
            if value == 0:  # 10, J, <PERSON>, <PERSON> → 11 cartes brûlées
                self.burn_probabilities[11] = prob
            else:  # Autres valeurs → value + 1 cartes brûlées
                self.burn_probabilities[value + 1] = prob

        print("Probabilités de brûlage:")
        for burn_cards, prob in sorted(self.burn_probabilities.items()):
            corresponding_count = self.card_distribution[0] if burn_cards == 11 else self.card_distribution[burn_cards - 1]
            print(f"  {burn_cards} cartes: {float(prob):.4f} ({corresponding_count}/{self.total_cards})")
    
    def simulate_hand_outcome(self, player_cards, banker_cards, remaining_cards):
        """Simule le résultat d'une main selon les règles exactes du Baccarat"""
        # Calcul des totaux initiaux
        player_total = sum(player_cards) % 10
        banker_total = sum(banker_cards) % 10
        
        cards_used = 4  # 2 + 2 cartes initiales
        card_index = 0
        
        # Vérification des naturels
        if player_total in [8, 9] or banker_total in [8, 9]:
            return {
                'cards_used': cards_used,
                'player_total': player_total,
                'banker_total': banker_total,
                'winner': 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
            }
        
        player_third_card = None
        
        # Règles de tirage du joueur
        if player_total <= 5:
            if card_index < len(remaining_cards):
                player_third_card = remaining_cards[card_index]
                player_total = (player_total + player_third_card) % 10
                cards_used += 1
                card_index += 1
        
        # Règles de tirage du banquier
        should_banker_draw = False
        
        if player_third_card is None:  # Joueur n'a pas tiré
            should_banker_draw = banker_total <= 5
        else:  # Joueur a tiré
            if banker_total <= 2:
                should_banker_draw = True
            elif banker_total == 3:
                should_banker_draw = player_third_card != 8
            elif banker_total == 4:
                should_banker_draw = player_third_card in [2, 3, 4, 5, 6, 7]
            elif banker_total == 5:
                should_banker_draw = player_third_card in [4, 5, 6, 7]
            elif banker_total == 6:
                should_banker_draw = player_third_card in [6, 7]
        
        if should_banker_draw and card_index < len(remaining_cards):
            banker_third_card = remaining_cards[card_index]
            banker_total = (banker_total + banker_third_card) % 10
            cards_used += 1
        
        return {
            'cards_used': cards_used,
            'player_total': player_total,
            'banker_total': banker_total,
            'winner': 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
        }
    
    def generate_realistic_card_sequence(self, burn_cards):
        """Génère une séquence réaliste de cartes basée sur la distribution"""
        remaining_cards = self.total_cards - burn_cards
        
        # Créer une séquence de cartes basée sur la distribution
        card_sequence = []
        
        # Ajouter les cartes selon la distribution (simplifiée)
        for value, count in self.card_distribution.items():
            # Ajuster le nombre selon le brûlage
            if value == 0 and burn_cards == 11:  # Une carte valeur 0 brûlée
                adjusted_count = count - 1
            elif value != 0 and burn_cards == value + 1:  # Une carte de cette valeur brûlée
                adjusted_count = count - 1
            else:
                adjusted_count = count
            
            card_sequence.extend([value] * adjusted_count)
        
        return card_sequence[:remaining_cards]
    
    def calculate_pure_even_combinations(self):
        """Calcule les combinaisons de parties purement paires"""
        total_pure_even = 0
        
        print("\n" + "="*60)
        print("CALCUL DES PARTIES PUREMENT PAIRES")
        print("="*60)
        
        for burn_cards, burn_prob in self.burn_probabilities.items():
            if burn_cards % 2 == 0:  # Brûlage pair
                print(f"\nBrûlage de {burn_cards} cartes (probabilité: {float(burn_prob):.4f})")
                
                # Générer séquence de cartes après brûlage
                card_sequence = self.generate_realistic_card_sequence(burn_cards)
                remaining_cards = len(card_sequence)
                
                print(f"  Cartes restantes: {remaining_cards}")
                
                # Simuler toutes les mains possibles
                hand_count = 0
                pure_even_hands = 0
                
                # Simulation simplifiée : on teste différentes configurations
                for start_pos in range(0, min(remaining_cards - 6, 100), 6):  # Échantillonnage
                    if start_pos + 6 <= len(card_sequence):
                        player_cards = [card_sequence[start_pos], card_sequence[start_pos + 2]]
                        banker_cards = [card_sequence[start_pos + 1], card_sequence[start_pos + 3]]
                        remaining_for_hand = card_sequence[start_pos + 4:start_pos + 10]
                        
                        result = self.simulate_hand_outcome(player_cards, banker_cards, remaining_for_hand)
                        hand_count += 1
                        
                        if result['cards_used'] % 2 == 0:  # Main paire
                            pure_even_hands += 1
                
                if hand_count > 0:
                    prob_even_hand = pure_even_hands / hand_count
                    print(f"  Mains testées: {hand_count}")
                    print(f"  Mains paires: {pure_even_hands}")
                    print(f"  Probabilité main paire: {prob_even_hand:.4f}")
                    
                    # Estimation du nombre de parties purement paires pour ce brûlage
                    # (Calcul simplifié)
                    estimated_hands_per_game = remaining_cards // 5  # Approximation
                    prob_all_even = prob_even_hand ** estimated_hands_per_game
                    
                    combinations_this_burn = float(burn_prob) * prob_all_even * (2 ** estimated_hands_per_game)
                    total_pure_even += combinations_this_burn
                    
                    print(f"  Mains par partie: ~{estimated_hands_per_game}")
                    print(f"  Prob toutes paires: {prob_all_even:.2e}")
                    print(f"  Combinaisons: {combinations_this_burn:.2e}")
        
        return int(total_pure_even)
    
    def calculate_pure_odd_combinations(self):
        """Calcule les combinaisons de parties purement impaires"""
        total_pure_odd = 0
        
        print("\n" + "="*60)
        print("CALCUL DES PARTIES PUREMENT IMPAIRES")
        print("="*60)
        
        for burn_cards, burn_prob in self.burn_probabilities.items():
            if burn_cards % 2 == 1:  # Brûlage impair
                print(f"\nBrûlage de {burn_cards} cartes (probabilité: {float(burn_prob):.4f})")
                
                # Générer séquence de cartes après brûlage
                card_sequence = self.generate_realistic_card_sequence(burn_cards)
                remaining_cards = len(card_sequence)
                
                print(f"  Cartes restantes: {remaining_cards}")
                
                # Simuler toutes les mains possibles
                hand_count = 0
                pure_odd_hands = 0
                
                # Simulation simplifiée
                for start_pos in range(0, min(remaining_cards - 6, 100), 6):
                    if start_pos + 6 <= len(card_sequence):
                        player_cards = [card_sequence[start_pos], card_sequence[start_pos + 2]]
                        banker_cards = [card_sequence[start_pos + 1], card_sequence[start_pos + 3]]
                        remaining_for_hand = card_sequence[start_pos + 4:start_pos + 10]
                        
                        result = self.simulate_hand_outcome(player_cards, banker_cards, remaining_for_hand)
                        hand_count += 1
                        
                        if result['cards_used'] % 2 == 1:  # Main impaire
                            pure_odd_hands += 1
                
                if hand_count > 0:
                    prob_odd_hand = pure_odd_hands / hand_count
                    print(f"  Mains testées: {hand_count}")
                    print(f"  Mains impaires: {pure_odd_hands}")
                    print(f"  Probabilité main impaire: {prob_odd_hand:.4f}")
                    
                    # Estimation du nombre de parties purement impaires pour ce brûlage
                    estimated_hands_per_game = remaining_cards // 5
                    prob_all_odd = prob_odd_hand ** estimated_hands_per_game
                    
                    combinations_this_burn = float(burn_prob) * prob_all_odd * (2 ** estimated_hands_per_game)
                    total_pure_odd += combinations_this_burn
                    
                    print(f"  Mains par partie: ~{estimated_hands_per_game}")
                    print(f"  Prob toutes impaires: {prob_all_odd:.2e}")
                    print(f"  Combinaisons: {combinations_this_burn:.2e}")
        
        return int(total_pure_odd)
    
    def run_realistic_analysis(self):
        """Exécute l'analyse complète avec distribution réaliste"""
        print("="*70)
        print("CALCULATEUR RÉALISTE BACCARAT")
        print("Basé sur la distribution réelle des cartes dans 312 cartes")
        print("="*70)
        
        # Calculs
        pure_even = self.calculate_pure_even_combinations()
        pure_odd = self.calculate_pure_odd_combinations()
        
        print("\n" + "="*70)
        print("RÉSULTATS FINAUX RÉALISTES")
        print("="*70)
        print(f"1. Parties purement paires: {pure_even:,}")
        print(f"2. Parties purement impaires: {pure_odd:,}")
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE RÉALISTE")
        print("="*70)
        print("- Distribution réelle: ~24 cartes par valeur dans 312 cartes")
        print("- Probabilités de brûlage basées sur la distribution")
        print("- Simulation des règles exactes de 3ème carte")
        print("- Prise en compte des valeurs réelles des cartes")
        print("- Calculs combinatoires basés sur les vraies probabilités")
        
        return {
            'pure_even': pure_even,
            'pure_odd': pure_odd
        }

if __name__ == "__main__":
    calculator = RealisticBaccaratCalculator()
    results = calculator.run_realistic_analysis()
