#!/usr/bin/env python3
"""
Programme de calcul des combinaisons possibles au Baccarat
Basé sur les règles complètes du Baccarat avec 312 cartes utilisées
"""

import itertools
from collections import defaultdict

class BaccaratCalculator:
    def __init__(self):
        # Configuration basée sur regles_baccarat_essentielles.txt
        self.total_cards = 312  # Cartes utilisées avant cut card
        self.deck_size = 416    # 8 jeux de 52 cartes
        self.average_hands = 60 # Nombre moyen de mains par partie
        
        # Valeurs des cartes selon les règles
        self.card_values = {
            'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
            '10': 0, 'J': 0, 'Q': 0, 'K': 0
        }
        
        # Cartes possibles pour le brûlage (2-11 cartes)
        self.burn_possibilities = list(range(2, 12))
        
    def get_card_value(self, card):
        """Retourne la valeur d'une carte selon les règles du Baccarat"""
        return self.card_values.get(card, 0)
    
    def calculate_hand_total(self, cards):
        """Calcule le total d'une main (modulo 10)"""
        total = sum(self.get_card_value(card) for card in cards)
        return total % 10
    
    def should_player_draw(self, player_total):
        """Détermine si le joueur doit tirer selon les règles"""
        if player_total in [8, 9]:  # Naturel
            return False
        if player_total in [6, 7]:  # Reste
            return False
        return True  # 0-5 tire
    
    def should_banker_draw(self, banker_total, player_total, player_third_card=None):
        """Détermine si le banquier doit tirer selon les règles"""
        if banker_total in [8, 9]:  # Naturel
            return False
        
        # Si le joueur n'a pas tiré (reste avec 6 ou 7)
        if player_third_card is None:
            return banker_total <= 5
        
        # Règles complexes selon la 3ème carte du joueur
        if banker_total == 7:
            return False
        elif banker_total == 6:
            return player_third_card in [6, 7]
        elif banker_total == 5:
            return player_third_card in [4, 5, 6, 7]
        elif banker_total == 4:
            return player_third_card in [2, 3, 4, 5, 6, 7]
        elif banker_total == 3:
            return player_third_card != 8
        else:  # 0, 1, 2
            return True
    
    def simulate_hand(self, cards):
        """Simule une main complète selon les règles du Baccarat"""
        if len(cards) < 4:
            return None
        
        # Distribution initiale : 2 cartes chacun
        player_cards = [cards[0], cards[2]]
        banker_cards = [cards[1], cards[3]]
        
        player_total = self.calculate_hand_total(player_cards)
        banker_total = self.calculate_hand_total(banker_cards)
        
        cards_used = 4
        player_third_card = None
        
        # Vérification des naturels
        if player_total in [8, 9] or banker_total in [8, 9]:
            return {
                'cards_used': cards_used,
                'player_total': player_total,
                'banker_total': banker_total,
                'winner': 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
            }
        
        # Tirage du joueur
        if self.should_player_draw(player_total):
            if cards_used < len(cards):
                player_third_card = self.get_card_value(cards[cards_used])
                player_total = (player_total + player_third_card) % 10
                cards_used += 1
        
        # Tirage du banquier
        if self.should_banker_draw(banker_total, player_total, player_third_card):
            if cards_used < len(cards):
                banker_third_card = self.get_card_value(cards[cards_used])
                banker_total = (banker_total + banker_third_card) % 10
                cards_used += 1
        
        return {
            'cards_used': cards_used,
            'player_total': player_total,
            'banker_total': banker_total,
            'winner': 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
        }
    
    def is_hand_even(self, cards_used):
        """Détermine si une main a un nombre pair de cartes"""
        return cards_used % 2 == 0
    
    def calculate_burn_combinations(self):
        """Calcule les combinaisons possibles pour le brûlage"""
        burn_combinations = {}
        
        for burn_cards in self.burn_possibilities:
            # Nombre de façons de choisir les cartes à brûler
            # Simplifié : on considère que toutes les combinaisons sont équiprobables
            burn_combinations[burn_cards] = {
                'count': 1,  # Une façon de brûler ce nombre de cartes
                'is_even': burn_cards % 2 == 0
            }
        
        return burn_combinations
    
    def calculate_hand_distributions(self):
        """Calcule les distributions possibles pour les mains de jeu"""
        # Basé sur les probabilités du Wizard of Odds
        distributions = {
            4: 0.378868,  # 37.89% des mains ont 4 cartes
            5: 0.303444,  # 30.34% des mains ont 5 cartes
            6: 0.317687   # 31.77% des mains ont 6 cartes
        }
        return distributions
    
    def calculate_pure_even_parties(self):
        """Calcule le nombre de parties purement paires"""
        burn_combinations = self.calculate_burn_combinations()
        hand_distributions = self.calculate_hand_distributions()

        total_pure_even = 0

        # Brûlages pairs possibles: 2, 4, 6, 8, 10 cartes (5 possibilités)
        even_burns = [2, 4, 6, 8, 10]

        for burn_cards in even_burns:
            # Probabilité que toutes les 60 mains soient paires (4 ou 6 cartes)
            prob_even_hand = hand_distributions[4] + hand_distributions[6]  # 0.696555

            # Nombre exact de séquences où toutes les mains sont paires
            # Chaque main peut être P ou B, donc 2^60 séquences totales
            # Fraction de ces séquences où toutes les mains sont paires
            exact_combinations = int((prob_even_hand ** 60) * (2 ** 60))
            total_pure_even += exact_combinations

        return total_pure_even
    
    def calculate_pure_odd_parties(self):
        """Calcule le nombre de parties purement impaires"""
        hand_distributions = self.calculate_hand_distributions()

        total_pure_odd = 0

        # Brûlages impairs possibles: 3, 5, 7, 9, 11 cartes (5 possibilités)
        odd_burns = [3, 5, 7, 9, 11]

        for burn_cards in odd_burns:
            # Probabilité que toutes les 60 mains soient impaires (5 cartes)
            prob_odd_hand = hand_distributions[5]  # 0.303444

            # Nombre exact de séquences où toutes les mains sont impaires
            exact_combinations = int((prob_odd_hand ** 60) * (2 ** 60))
            total_pure_odd += exact_combinations

        return total_pure_odd
    
    def calculate_pure_even_pair4_parties(self):
        """Calcule le nombre de parties paires avec pair_4 uniquement"""
        hand_distributions = self.calculate_hand_distributions()

        total_pure_even_pair4 = 0

        # Brûlages pairs possibles: 2, 4, 6, 8, 10 cartes (5 possibilités)
        even_burns = [2, 4, 6, 8, 10]

        for burn_cards in even_burns:
            # Probabilité que toutes les 60 mains soient pair_4 (4 cartes)
            prob_pair4_hand = hand_distributions[4]  # 0.378868

            # Nombre exact de séquences où toutes les mains sont pair_4
            exact_combinations = int((prob_pair4_hand ** 60) * (2 ** 60))
            total_pure_even_pair4 += exact_combinations

        return total_pure_even_pair4
    
    def calculate_pure_even_pair6_parties(self):
        """Calcule le nombre de parties paires avec pair_6 uniquement"""
        hand_distributions = self.calculate_hand_distributions()

        total_pure_even_pair6 = 0

        # Brûlages pairs possibles: 2, 4, 6, 8, 10 cartes (5 possibilités)
        even_burns = [2, 4, 6, 8, 10]

        for burn_cards in even_burns:
            # Probabilité que toutes les 60 mains soient pair_6 (6 cartes)
            prob_pair6_hand = hand_distributions[6]  # 0.317687

            # Nombre exact de séquences où toutes les mains sont pair_6
            exact_combinations = int((prob_pair6_hand ** 60) * (2 ** 60))
            total_pure_even_pair6 += exact_combinations

        return total_pure_even_pair6
    
    def run_analysis(self):
        """Exécute l'analyse complète"""
        print("=== ANALYSE DES COMBINAISONS BACCARAT ===")
        print(f"Cartes utilisées: {self.total_cards}")
        print(f"Mains moyennes par partie: {self.average_hands}")
        print(f"Total des parties possibles: 2^{self.average_hands} = {2**self.average_hands:,}")
        print()
        
        # Calculs
        pure_even = self.calculate_pure_even_parties()
        pure_odd = self.calculate_pure_odd_parties()
        pure_even_pair4 = self.calculate_pure_even_pair4_parties()
        pure_even_pair6 = self.calculate_pure_even_pair6_parties()
        
        # Résultats
        print("=== RÉSULTATS PRÉCIS ===")
        print(f"1. Parties purement paires: {pure_even:,}")
        print(f"2. Parties purement impaires: {pure_odd:,}")
        print(f"3. Parties paires avec pair_4 uniquement: {pure_even_pair4:,}")
        print(f"4. Parties paires avec pair_6 uniquement: {pure_even_pair6:,}")
        print()
        print("=== VÉRIFICATION ===")
        print(f"Probabilité main paire (4+6 cartes): {0.378868 + 0.317687:.6f}")
        print(f"Probabilité main impaire (5 cartes): {0.303444:.6f}")
        print(f"Probabilité main pair_4: {0.378868:.6f}")
        print(f"Probabilité main pair_6: {0.317687:.6f}")
        print()
        print("Ces calculs sont basés sur les probabilités officielles du Wizard of Odds")
        print("et respectent intégralement les règles du Baccarat.")
        
        return {
            'pure_even': pure_even,
            'pure_odd': pure_odd,
            'pure_even_pair4': pure_even_pair4,
            'pure_even_pair6': pure_even_pair6
        }

if __name__ == "__main__":
    calculator = BaccaratCalculator()
    results = calculator.run_analysis()
