#!/usr/bin/env python3
"""
Calculateur exact des combinaisons Baccarat
Utilise les fractions exactes pour éviter les erreurs d'arrondi
"""

from fractions import Fraction
from decimal import Decimal, getcontext

# Augmenter la précision pour les calculs
getcontext().prec = 50

class ExactBaccaratCalculator:
    def __init__(self):
        # Configuration basée sur regles_baccarat_essentielles.txt
        self.total_deck_cards = 416  # 8 jeux de 52 cartes
        self.cut_card_position = 312  # Cut card aux 3/4 (416 * 3/4 = 312)
        self.cards_used = 312  # Cartes utilisées avant cut card
        self.average_hands = 60
        self.total_possible_games = 2 ** 60
        
        # Probabilités exactes du Wizard of Odds (8 jeux)
        self.prob_4_cards = Fraction(378868, 1000000)  # 0.378868
        self.prob_5_cards = Fraction(303444, 1000000)  # 0.303444
        self.prob_6_cards = Fraction(317687, 1000000)  # 0.317687 (reste pour faire 1.0)
        
        # Vérification que la somme = 1
        total_prob = self.prob_4_cards + self.prob_5_cards + self.prob_6_cards
        print(f"Vérification probabilités: {float(total_prob):.6f}")
        
        # Brûlages possibles
        self.even_burns = [2, 4, 6, 8, 10]  # 5 possibilités
        self.odd_burns = [3, 5, 7, 9, 11]   # 5 possibilités
    
    def calculate_exact_pure_even_parties(self):
        """Calcule exactement les parties purement paires"""
        # Probabilité qu'une main soit paire (4 ou 6 cartes)
        prob_even_hand = self.prob_4_cards + self.prob_6_cards
        
        # Probabilité que toutes les 60 mains soient paires
        prob_all_even = prob_even_hand ** 60
        
        # Nombre de brûlages pairs possibles
        num_even_burns = len(self.even_burns)
        
        # Calcul exact
        exact_result = num_even_burns * prob_all_even * self.total_possible_games
        
        return int(exact_result), float(prob_even_hand), float(prob_all_even)
    
    def calculate_exact_pure_odd_parties(self):
        """Calcule exactement les parties purement impaires"""
        # Probabilité qu'une main soit impaire (5 cartes)
        prob_odd_hand = self.prob_5_cards
        
        # Probabilité que toutes les 60 mains soient impaires
        prob_all_odd = prob_odd_hand ** 60
        
        # Nombre de brûlages impairs possibles
        num_odd_burns = len(self.odd_burns)
        
        # Calcul exact
        exact_result = num_odd_burns * prob_all_odd * self.total_possible_games
        
        return int(exact_result), float(prob_odd_hand), float(prob_all_odd)
    
    def calculate_exact_pure_even_pair4_parties(self):
        """Calcule exactement les parties paires avec pair_4 uniquement"""
        # Probabilité qu'une main soit pair_4 (4 cartes)
        prob_pair4_hand = self.prob_4_cards
        
        # Probabilité que toutes les 60 mains soient pair_4
        prob_all_pair4 = prob_pair4_hand ** 60
        
        # Nombre de brûlages pairs possibles
        num_even_burns = len(self.even_burns)
        
        # Calcul exact
        exact_result = num_even_burns * prob_all_pair4 * self.total_possible_games
        
        return int(exact_result), float(prob_pair4_hand), float(prob_all_pair4)
    
    def calculate_exact_pure_even_pair6_parties(self):
        """Calcule exactement les parties paires avec pair_6 uniquement"""
        # Probabilité qu'une main soit pair_6 (6 cartes)
        prob_pair6_hand = self.prob_6_cards
        
        # Probabilité que toutes les 60 mains soient pair_6
        prob_all_pair6 = prob_pair6_hand ** 60
        
        # Nombre de brûlages pairs possibles
        num_even_burns = len(self.even_burns)
        
        # Calcul exact
        exact_result = num_even_burns * prob_all_pair6 * self.total_possible_games
        
        return int(exact_result), float(prob_pair6_hand), float(prob_all_pair6)
    
    def run_exact_analysis(self):
        """Exécute l'analyse exacte complète"""
        print("=" * 60)
        print("CALCULATEUR EXACT DES COMBINAISONS BACCARAT")
        print("=" * 60)
        print(f"Total cartes dans le sabot: {self.total_deck_cards} (8 jeux)")
        print(f"Position cut card: {self.cut_card_position} (aux 3/4)")
        print(f"Cartes utilisées avant cut card: {self.cards_used}")
        print(f"Mains par partie: {self.average_hands}")
        print(f"Total parties possibles: 2^{self.average_hands} = {self.total_possible_games:,}")
        print()
        
        # Calculs exacts
        pure_even, prob_even, prob_all_even = self.calculate_exact_pure_even_parties()
        pure_odd, prob_odd, prob_all_odd = self.calculate_exact_pure_odd_parties()
        pure_pair4, prob_pair4, prob_all_pair4 = self.calculate_exact_pure_even_pair4_parties()
        pure_pair6, prob_pair6, prob_all_pair6 = self.calculate_exact_pure_even_pair6_parties()
        
        print("=" * 60)
        print("RÉSULTATS EXACTS")
        print("=" * 60)
        print(f"1. Parties purement paires: {pure_even:,}")
        print(f"   - Probabilité main paire: {prob_even:.6f}")
        print(f"   - Probabilité 60 mains paires: {prob_all_even:.2e}")
        print()
        
        print(f"2. Parties purement impaires: {pure_odd:,}")
        print(f"   - Probabilité main impaire: {prob_odd:.6f}")
        print(f"   - Probabilité 60 mains impaires: {prob_all_odd:.2e}")
        print()
        
        print(f"3. Parties paires avec pair_4 uniquement: {pure_pair4:,}")
        print(f"   - Probabilité main pair_4: {prob_pair4:.6f}")
        print(f"   - Probabilité 60 mains pair_4: {prob_all_pair4:.2e}")
        print()
        
        print(f"4. Parties paires avec pair_6 uniquement: {pure_pair6:,}")
        print(f"   - Probabilité main pair_6: {prob_pair6:.6f}")
        print(f"   - Probabilité 60 mains pair_6: {prob_all_pair6:.2e}")
        print()
        
        print("=" * 60)
        print("ANALYSE DES RÉSULTATS")
        print("=" * 60)
        
        # Analyse des résultats
        if pure_even > 0:
            ratio_even = self.total_possible_games / pure_even
            print(f"Parties purement paires: 1 chance sur {ratio_even:,.0f}")
        else:
            print("Parties purement paires: Aucune (probabilité trop faible)")
        
        if pure_odd > 0:
            ratio_odd = self.total_possible_games / pure_odd
            print(f"Parties purement impaires: 1 chance sur {ratio_odd:,.0f}")
        else:
            print("Parties purement impaires: Aucune (probabilité trop faible)")
        
        if pure_pair4 > 0:
            ratio_pair4 = self.total_possible_games / pure_pair4
            print(f"Parties pair_4 uniquement: 1 chance sur {ratio_pair4:,.0f}")
        else:
            print("Parties pair_4 uniquement: Aucune (probabilité trop faible)")
        
        if pure_pair6 > 0:
            ratio_pair6 = self.total_possible_games / pure_pair6
            print(f"Parties pair_6 uniquement: 1 chance sur {ratio_pair6:,.0f}")
        else:
            print("Parties pair_6 uniquement: Aucune (probabilité trop faible)")
        
        print()
        print("=" * 60)
        print("CONCLUSION")
        print("=" * 60)
        print("Ces calculs sont basés sur:")
        print("- Les probabilités officielles du Wizard of Odds")
        print("- Les règles complètes du Baccarat")
        print(f"- {self.total_deck_cards} cartes au total (8 jeux de 52)")
        print(f"- Cut card aux 3/4 = {self.cut_card_position} cartes")
        print(f"- {self.cards_used} cartes utilisées (brûlage compris)")
        print("- 60 mains par partie en moyenne")
        print("- Calculs exacts sans approximation")
        
        return {
            'pure_even': pure_even,
            'pure_odd': pure_odd,
            'pure_pair4': pure_pair4,
            'pure_pair6': pure_pair6
        }

if __name__ == "__main__":
    calculator = ExactBaccaratCalculator()
    results = calculator.run_exact_analysis()
