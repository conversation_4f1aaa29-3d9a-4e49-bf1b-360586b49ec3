#!/usr/bin/env python3
"""
Calculateur des probabilités pour toutes les longueurs de séquences
De longueur 1 jusqu'à la longueur maximale pour chaque type
"""

import math

class BaccaratAllLengthsProbabilities:
    def __init__(self):
        self.total_cards = 312
        
        # Probabilités officielles basées sur les règles du Baccarat (8 decks)
        self.hand_probabilities = {
            'natural_4_cards': 0.458597,      # Naturels + aucune 3ème carte
            'single_draw_5_cards': 0.446247,  # Un seul joueur tire
            'double_draw_6_cards': 0.095156   # Les deux joueurs tirent
        }
        
        # Longueurs maximales par brûlage
        self.max_lengths = {
            2: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            3: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            4: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            5: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            6: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            7: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            8: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            9: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            10: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            11: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0}
        }
        
        print("Calculateur des probabilités pour toutes les longueurs")
        print("De longueur 1 jusqu'à la longueur maximale")
    
    def calculate_sequence_probability(self, base_prob, length):
        """Calcule la probabilité d'une séquence de longueur donnée"""
        return base_prob ** length
    
    def format_probability(self, prob):
        """Formate une probabilité pour affichage"""
        if prob == 0:
            return "0"
        elif prob >= 1e-3:
            return f"{prob:.6f}"
        else:
            return f"{prob:.2e}"
    
    def format_odds(self, prob):
        """Formate les chances (1 sur X)"""
        if prob == 0:
            return "Impossible"
        elif prob >= 1:
            return "Certain"
        else:
            return f"1 sur {1/prob:.2e}"
    
    def analyze_burn_all_lengths(self, burn_cards):
        """Analyse toutes les longueurs pour un brûlage donné"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*80}")
        print(f"BRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'}) - {remaining_cards} cartes restantes")
        print("="*80)
        
        max_data = self.max_lengths[burn_cards]
        results = {}
        
        if burn_is_even:
            # Analyser les séquences paires
            if max_data['order_4'] > 0:
                results['order_4'] = self.analyze_order_4_all_lengths(max_data['order_4'])
            
            if max_data['order_6'] > 0:
                results['order_6'] = self.analyze_order_6_all_lengths(max_data['order_6'])
            
            if max_data['mixed_4_6'] > 0:
                results['mixed_4_6'] = self.analyze_mixed_all_lengths(max_data['mixed_4_6'])
        else:
            # Analyser les séquences impaires
            if max_data['order_5'] > 0:
                results['order_5'] = self.analyze_order_5_all_lengths(max_data['order_5'])
        
        return results
    
    def analyze_order_4_all_lengths(self, max_length):
        """Analyse toutes les longueurs pour ordre 4 (naturels)"""
        print(f"\nORDRE 4 (Naturels) - Longueurs de 1 à {max_length}:")
        print("-" * 60)
        print("Longueur | Probabilité  | Chances (1 sur)")
        print("-" * 60)
        
        base_prob = self.hand_probabilities['natural_4_cards']
        results = {}
        
        # Calculer pour quelques longueurs clés
        key_lengths = [1, 2, 3, 4, 5, 10, 15, 20, 25, max_length]
        key_lengths = [l for l in key_lengths if l <= max_length]
        
        for length in key_lengths:
            prob = self.calculate_sequence_probability(base_prob, length)
            results[length] = prob
            
            print(f"   {length:2d}    | {self.format_probability(prob):>10} | {self.format_odds(prob)}")
        
        return results
    
    def analyze_order_6_all_lengths(self, max_length):
        """Analyse toutes les longueurs pour ordre 6 (doubles tirages)"""
        print(f"\nORDRE 6 (Doubles tirages) - Longueurs de 1 à {max_length}:")
        print("-" * 60)
        print("Longueur | Probabilité  | Chances (1 sur)")
        print("-" * 60)
        
        base_prob = self.hand_probabilities['double_draw_6_cards']
        results = {}
        
        # Calculer pour quelques longueurs clés
        key_lengths = [1, 2, 3, 4, 5, 10, 15, 20, 25, 30, 35, max_length]
        key_lengths = [l for l in key_lengths if l <= max_length]
        
        for length in key_lengths:
            prob = self.calculate_sequence_probability(base_prob, length)
            results[length] = prob
            
            print(f"   {length:2d}    | {self.format_probability(prob):>10} | {self.format_odds(prob)}")
        
        return results
    
    def analyze_order_5_all_lengths(self, max_length):
        """Analyse toutes les longueurs pour ordre 5 (tirages uniques)"""
        print(f"\nORDRE 5 (Tirages uniques) - Longueurs de 1 à {max_length}:")
        print("-" * 60)
        print("Longueur | Probabilité  | Chances (1 sur)")
        print("-" * 60)
        
        base_prob = self.hand_probabilities['single_draw_5_cards']
        results = {}
        
        # Calculer pour quelques longueurs clés
        key_lengths = [1, 2, 3, 4, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, max_length]
        key_lengths = [l for l in key_lengths if l <= max_length]
        
        for length in key_lengths:
            prob = self.calculate_sequence_probability(base_prob, length)
            results[length] = prob
            
            print(f"   {length:2d}    | {self.format_probability(prob):>10} | {self.format_odds(prob)}")
        
        return results
    
    def analyze_mixed_all_lengths(self, max_length):
        """Analyse toutes les longueurs pour séquences mixtes"""
        print(f"\nMIXTE 4+6 - Longueurs de 1 à {max_length}:")
        print("-" * 60)
        print("Longueur | Probabilité  | Chances (1 sur)")
        print("-" * 60)
        
        # Pour les séquences mixtes, utiliser une probabilité moyenne
        prob_4 = self.hand_probabilities['natural_4_cards']
        prob_6 = self.hand_probabilities['double_draw_6_cards']
        avg_prob = (prob_4 + prob_6) / 2  # Approximation
        
        results = {}
        
        # Calculer pour quelques longueurs clés
        key_lengths = [1, 2, 3, 4, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, max_length]
        key_lengths = [l for l in key_lengths if l <= max_length]
        
        for length in key_lengths:
            prob = self.calculate_sequence_probability(avg_prob, length)
            results[length] = prob
            
            print(f"   {length:2d}    | {self.format_probability(prob):>10} | {self.format_odds(prob)}")
        
        return results
    
    def find_practical_thresholds(self):
        """Trouve les seuils pratiques de probabilité"""
        print("\n" + "="*80)
        print("SEUILS PRATIQUES DE PROBABILITÉ")
        print("="*80)
        
        thresholds = {
            'très_probable': 1e-1,      # 10%
            'probable': 1e-2,           # 1%
            'possible': 1e-3,           # 0.1%
            'rare': 1e-6,               # 1 sur 1 million
            'très_rare': 1e-9,          # 1 sur 1 milliard
            'quasi_impossible': 1e-12   # 1 sur 1 trillion
        }
        
        print("Seuils de référence:")
        for name, threshold in thresholds.items():
            print(f"- {name.replace('_', ' ').title()}: {threshold:.0e} ({self.format_odds(threshold)})")
        
        return thresholds
    
    def run_complete_analysis(self):
        """Exécute l'analyse complète pour tous les brûlages"""
        
        print("="*80)
        print("CALCULATEUR DES PROBABILITÉS POUR TOUTES LES LONGUEURS")
        print("De longueur 1 jusqu'à la longueur maximale pour chaque type")
        print("="*80)
        
        print("\nPROBABILITÉS DE BASE:")
        print(f"- Naturel (4 cartes): {self.hand_probabilities['natural_4_cards']:.6f}")
        print(f"- Tirage unique (5 cartes): {self.hand_probabilities['single_draw_5_cards']:.6f}")
        print(f"- Double tirage (6 cartes): {self.hand_probabilities['double_draw_6_cards']:.6f}")
        
        # Analyser les seuils pratiques
        thresholds = self.find_practical_thresholds()
        
        all_results = {}
        
        # Analyser chaque brûlage
        for burn_cards in range(2, 12):
            result = self.analyze_burn_all_lengths(burn_cards)
            all_results[burn_cards] = result
        
        print("\n" + "="*80)
        print("RÉSUMÉ DES LONGUEURS PRATIQUES")
        print("="*80)
        
        # Trouver les longueurs pratiques pour chaque type
        self.summarize_practical_lengths(all_results, thresholds)
        
        return {
            'results_by_burn': all_results,
            'thresholds': thresholds
        }
    
    def summarize_practical_lengths(self, all_results, thresholds):
        """Résume les longueurs pratiques selon les seuils"""
        
        print("\nLongueurs maximales selon les seuils de probabilité:")
        print("-" * 60)
        
        # Analyser ordre 4
        print("\nORDRE 4 (Naturels):")
        base_prob = self.hand_probabilities['natural_4_cards']
        for name, threshold in thresholds.items():
            max_length = int(math.log(threshold) / math.log(base_prob))
            if max_length > 0:
                print(f"- {name.replace('_', ' ').title()}: jusqu'à {max_length} mains consécutives")
        
        # Analyser ordre 6
        print("\nORDRE 6 (Doubles tirages):")
        base_prob = self.hand_probabilities['double_draw_6_cards']
        for name, threshold in thresholds.items():
            max_length = int(math.log(threshold) / math.log(base_prob))
            if max_length > 0:
                print(f"- {name.replace('_', ' ').title()}: jusqu'à {max_length} mains consécutives")
        
        # Analyser ordre 5
        print("\nORDRE 5 (Tirages uniques):")
        base_prob = self.hand_probabilities['single_draw_5_cards']
        for name, threshold in thresholds.items():
            max_length = int(math.log(threshold) / math.log(base_prob))
            if max_length > 0:
                print(f"- {name.replace('_', ' ').title()}: jusqu'à {max_length} mains consécutives")

if __name__ == "__main__":
    calculator = BaccaratAllLengthsProbabilities()
    final_results = calculator.run_complete_analysis()
