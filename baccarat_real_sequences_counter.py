#!/usr/bin/env python3
"""
Compteur de séquences réelles de cartes Baccarat
Compte les séquences qui produisent des parties purement paires/impaires
"""

import itertools
import random
from collections import defaultdict

class BaccaratRealSequencesCounter:
    def __init__(self):
        # Configuration
        self.total_cards = 312
        self.deck_size = 416
        
        # Création du deck complet (8 jeux)
        self.full_deck = self.create_full_deck()
        
        # Valeurs Baccarat
        self.card_values = {
            'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
            '10': 0, 'J': 0, 'Q': 0, 'K': 0
        }
        
        print("Compteur de séquences réelles Baccarat")
        print(f"Deck complet: {len(self.full_deck)} cartes")
        print(f"Cartes par partie: {self.total_cards}")
    
    def create_full_deck(self):
        """Crée un deck complet de 8 jeux"""
        suits = ['♠', '♥', '♦', '♣']
        ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
        
        deck = []
        for _ in range(8):  # 8 jeux
            for suit in suits:
                for rank in ranks:
                    deck.append(rank)
        
        return deck
    
    def get_baccarat_value(self, card):
        """Retourne la valeur Baccarat d'une carte"""
        return self.card_values[card]
    
    def calculate_hand_total(self, cards):
        """Calcule le total d'une main (modulo 10)"""
        total = sum(self.get_baccarat_value(card) for card in cards)
        return total % 10
    
    def apply_burn_rules(self, sequence):
        """Applique les règles de brûlage selon la première carte"""
        if len(sequence) == 0:
            return [], 0
        
        burn_card = sequence[0]
        burn_value = self.get_baccarat_value(burn_card)
        
        # Calculer le nombre de cartes à brûler selon les règles
        if burn_card in ['10', 'J', 'Q', 'K']:
            cards_to_burn = 11  # 1 + 10 supplémentaires
        else:
            cards_to_burn = 1 + burn_value  # 1 + valeur
        
        remaining_sequence = sequence[cards_to_burn:]
        return remaining_sequence, cards_to_burn
    
    def should_player_draw(self, player_total):
        """Règles de tirage du joueur"""
        if player_total in [8, 9]:  # Naturel
            return False
        if player_total in [6, 7]:  # Reste
            return False
        return True  # 0-5 tire
    
    def should_banker_draw(self, banker_total, player_total, player_third_card_value=None):
        """Règles de tirage du banquier selon le tableau officiel"""
        if banker_total in [8, 9]:  # Naturel
            return False
        
        # Si le joueur n'a pas tiré
        if player_third_card_value is None:
            return banker_total <= 5
        
        # Tableau officiel
        if banker_total == 7:
            return False
        elif banker_total == 6:
            return player_third_card_value in [6, 7]
        elif banker_total == 5:
            return player_third_card_value in [4, 5, 6, 7]
        elif banker_total == 4:
            return player_third_card_value in [2, 3, 4, 5, 6, 7]
        elif banker_total == 3:
            return player_third_card_value != 8
        else:  # 0, 1, 2
            return True
    
    def play_hand(self, sequence, start_index):
        """Joue une main selon les vraies règles"""
        if start_index + 4 > len(sequence):
            return None, start_index
        
        # Distribution initiale
        player_cards = [sequence[start_index], sequence[start_index + 2]]
        banker_cards = [sequence[start_index + 1], sequence[start_index + 3]]
        
        player_total = self.calculate_hand_total(player_cards)
        banker_total = self.calculate_hand_total(banker_cards)
        
        cards_used = 4
        current_index = start_index + 4
        player_third_card_value = None
        
        # Vérification des naturels
        if player_total in [8, 9] or banker_total in [8, 9]:
            return {
                'cards_used': cards_used,
                'player_total': player_total,
                'banker_total': banker_total,
                'winner': 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
            }, current_index
        
        # Tirage du joueur
        if self.should_player_draw(player_total):
            if current_index < len(sequence):
                player_third_card = sequence[current_index]
                player_third_card_value = self.get_baccarat_value(player_third_card)
                player_total = (player_total + player_third_card_value) % 10
                cards_used += 1
                current_index += 1
        
        # Tirage du banquier
        if self.should_banker_draw(banker_total, player_total, player_third_card_value):
            if current_index < len(sequence):
                banker_third_card_value = self.get_baccarat_value(sequence[current_index])
                banker_total = (banker_total + banker_third_card_value) % 10
                cards_used += 1
                current_index += 1
        
        winner = 'P' if player_total > banker_total else 'B' if banker_total > player_total else 'T'
        
        return {
            'cards_used': cards_used,
            'player_total': player_total,
            'banker_total': banker_total,
            'winner': winner
        }, current_index
    
    def analyze_sequence(self, sequence):
        """Analyse une séquence complète de 312 cartes"""
        # Appliquer le brûlage
        remaining_sequence, burn_cards = self.apply_burn_rules(sequence)
        
        if len(remaining_sequence) < 4:
            return None
        
        result = {
            'burn_cards': burn_cards,
            'burn_is_even': burn_cards % 2 == 0,
            'hands': [],
            'all_hands_even': True,
            'all_hands_odd': True
        }
        
        current_index = 0
        
        # Jouer toutes les mains possibles
        while current_index < len(remaining_sequence) - 3:
            hand_result, new_index = self.play_hand(remaining_sequence, current_index)
            
            if hand_result is None:
                break
            
            result['hands'].append(hand_result)
            
            # Vérifier parité
            hand_is_even = hand_result['cards_used'] % 2 == 0
            if not hand_is_even:
                result['all_hands_even'] = False
            if hand_is_even:
                result['all_hands_odd'] = False
            
            current_index = new_index
        
        return result
    
    def count_pure_sequences_sampling(self, num_samples=1000000):
        """Compte les séquences pures par échantillonnage"""
        pure_even_count = 0
        pure_odd_count = 0
        pure_even_pair4_count = 0
        pure_even_pair6_count = 0
        
        print(f"\nAnalyse de {num_samples:,} séquences aléatoires...")
        
        for i in range(num_samples):
            if i % 50000 == 0:
                print(f"Progression: {i:,}/{num_samples:,}")
            
            # Générer une séquence aléatoire de 312 cartes
            deck_copy = self.full_deck.copy()
            random.shuffle(deck_copy)
            sequence = deck_copy[:self.total_cards]
            
            # Analyser la séquence
            analysis = self.analyze_sequence(sequence)
            
            if analysis is None:
                continue
            
            # Vérifier les conditions
            if analysis['burn_is_even'] and analysis['all_hands_even']:
                pure_even_count += 1
                
                # Vérifier pair_4 uniquement
                all_hands_4 = all(hand['cards_used'] == 4 for hand in analysis['hands'])
                if all_hands_4:
                    pure_even_pair4_count += 1
                
                # Vérifier pair_6 uniquement
                all_hands_6 = all(hand['cards_used'] == 6 for hand in analysis['hands'])
                if all_hands_6:
                    pure_even_pair6_count += 1
            
            if not analysis['burn_is_even'] and analysis['all_hands_odd']:
                pure_odd_count += 1
        
        return {
            'pure_even': pure_even_count,
            'pure_odd': pure_odd_count,
            'pure_even_pair4': pure_even_pair4_count,
            'pure_even_pair6': pure_even_pair6_count,
            'total_samples': num_samples
        }
    
    def estimate_total_sequences(self, sample_results):
        """Estime le nombre total de séquences dans l'espace complet"""
        # Nombre total théorique de façons de choisir 312 cartes parmi 416
        # C(416, 312) × 312! arrangements
        # Approximation : utiliser les probabilités échantillonnées
        
        total_samples = sample_results['total_samples']
        
        if total_samples == 0:
            return sample_results
        
        # Probabilités estimées
        prob_pure_even = sample_results['pure_even'] / total_samples
        prob_pure_odd = sample_results['pure_odd'] / total_samples
        prob_pure_pair4 = sample_results['pure_even_pair4'] / total_samples
        prob_pure_pair6 = sample_results['pure_even_pair6'] / total_samples
        
        print(f"\nProbabilités estimées:")
        print(f"Parties purement paires: {prob_pure_even:.8f}")
        print(f"Parties purement impaires: {prob_pure_odd:.8f}")
        print(f"Parties pair_4 uniquement: {prob_pure_pair4:.8f}")
        print(f"Parties pair_6 uniquement: {prob_pure_pair6:.8f}")
        
        return sample_results
    
    def run_analysis(self):
        """Exécute l'analyse complète"""
        print("="*70)
        print("COMPTEUR DE SÉQUENCES RÉELLES BACCARAT")
        print("Analyse des vraies séquences de cartes")
        print("="*70)
        
        # Échantillonnage
        results = self.count_pure_sequences_sampling(1000000)
        
        print("\n" + "="*70)
        print("RÉSULTATS DE L'ÉCHANTILLONNAGE")
        print("="*70)
        print(f"Échantillons analysés: {results['total_samples']:,}")
        print(f"Séquences purement paires trouvées: {results['pure_even']:,}")
        print(f"Séquences purement impaires trouvées: {results['pure_odd']:,}")
        print(f"Séquences pair_4 uniquement: {results['pure_even_pair4']:,}")
        print(f"Séquences pair_6 uniquement: {results['pure_even_pair6']:,}")
        
        # Estimation
        self.estimate_total_sequences(results)
        
        print("\n" + "="*70)
        print("MÉTHODOLOGIE")
        print("="*70)
        print("✓ Séquences réelles de cartes avec valeurs exactes")
        print("✓ Application stricte du tableau de tirage officiel")
        print("✓ Règles de brûlage selon la première carte")
        print("✓ Échantillonnage Monte Carlo sur l'espace des séquences")
        print("✓ Estimation des probabilités réelles")
        
        return results

if __name__ == "__main__":
    counter = BaccaratRealSequencesCounter()
    final_results = counter.run_analysis()
