#!/usr/bin/env python3
"""
Calculateur exact des parties par ordre avec toutes les contraintes
Détermine le nombre de parties d'ordre 4, 6, 5 et mixtes possibles
"""

class BaccaratExactPartiesByOrder:
    def __init__(self):
        self.total_cards = 312
        
        # Composition exacte du deck (8 jeux)
        self.deck_composition = {
            0: 128,  # 10, J, Q, K
            1: 32,   # As
            2: 32, 3: 32, 4: 32, 5: 32, 6: 32, 7: 32, 8: 32, 9: 32
        }
        
        print("Calculateur exact des parties par ordre")
        print(f"Total cartes: {self.total_cards}")
        print("Contraintes: Règles officielles + 312 cartes + brûlage")
    
    def analyze_naturals_constraints(self):
        """Analyse les contraintes pour forcer QUE des naturels (4 cartes)"""
        print("\n" + "="*50)
        print("ANALYSE DES CONTRAINTES POUR NATURELS (4 CARTES)")
        print("="*50)
        
        # Pour avoir un naturel: Player OU Banker doit avoir 8 ou 9
        # Combinaisons qui donnent 8 ou 9 (modulo 10)
        natural_combinations = []
        
        for val1 in range(10):
            for val2 in range(10):
                total = (val1 + val2) % 10
                if total in [8, 9]:
                    natural_combinations.append((val1, val2, total))
        
        print(f"Combinaisons donnant naturel: {len(natural_combinations)}")
        print("Exemples:", natural_combinations[:5])
        
        # Probabilité théorique d'un naturel
        total_combinations = 10 * 10  # 100 combinaisons possibles
        natural_prob = len(natural_combinations) / total_combinations
        print(f"Probabilité théorique d'un naturel: {natural_prob:.3f}")
        
        return len(natural_combinations), natural_prob
    
    def analyze_double_draw_constraints(self):
        """Analyse les contraintes pour forcer QUE des doubles tirages (6 cartes)"""
        print("\n" + "="*50)
        print("ANALYSE DES CONTRAINTES POUR DOUBLES TIRAGES (6 CARTES)")
        print("="*50)
        
        # Pour avoir 6 cartes: Player tire ET Banker tire
        double_draw_count = 0
        
        for p1 in range(10):
            for p2 in range(10):
                for b1 in range(10):
                    for b2 in range(10):
                        player_total = (p1 + p2) % 10
                        banker_total = (b1 + b2) % 10
                        
                        # Vérifier naturels
                        if player_total in [8, 9] or banker_total in [8, 9]:
                            continue  # Pas de double tirage
                        
                        # Player tire ?
                        player_draws = player_total <= 5
                        
                        if player_draws:
                            # Analyser toutes les 3èmes cartes possibles
                            for p3 in range(10):
                                # Banker tire selon le tableau ?
                                banker_draws = self.banker_draws_with_player_third(banker_total, p3)
                                if banker_draws:
                                    double_draw_count += 1
        
        total_combinations = 10**5  # 100,000 combinaisons avec 3ème carte
        double_draw_prob = double_draw_count / total_combinations
        print(f"Combinaisons donnant double tirage: {double_draw_count}")
        print(f"Probabilité théorique double tirage: {double_draw_prob:.3f}")
        
        return double_draw_count, double_draw_prob
    
    def banker_draws_with_player_third(self, banker_total, player_third_value):
        """Détermine si Banker tire selon le tableau officiel"""
        if banker_total == 7:
            return False
        elif banker_total == 6:
            return player_third_value in [6, 7]
        elif banker_total == 5:
            return player_third_value in [4, 5, 6, 7]
        elif banker_total == 4:
            return player_third_value in [2, 3, 4, 5, 6, 7]
        elif banker_total == 3:
            return player_third_value != 8
        else:  # 0, 1, 2
            return True
    
    def analyze_single_draw_constraints(self):
        """Analyse les contraintes pour forcer QUE des tirages uniques (5 cartes)"""
        print("\n" + "="*50)
        print("ANALYSE DES CONTRAINTES POUR TIRAGES UNIQUES (5 CARTES)")
        print("="*50)
        
        # Pour avoir 5 cartes: exactement un joueur tire
        single_draw_count = 0
        
        for p1 in range(10):
            for p2 in range(10):
                for b1 in range(10):
                    for b2 in range(10):
                        player_total = (p1 + p2) % 10
                        banker_total = (b1 + b2) % 10
                        
                        # Vérifier naturels
                        if player_total in [8, 9] or banker_total in [8, 9]:
                            continue
                        
                        # Player tire ?
                        player_draws = player_total <= 5
                        
                        if player_draws:
                            # Player tire, vérifier si Banker ne tire pas
                            for p3 in range(10):
                                banker_draws = self.banker_draws_with_player_third(banker_total, p3)
                                if not banker_draws:  # Seul Player tire
                                    single_draw_count += 1
                        else:
                            # Player ne tire pas, vérifier si Banker tire
                            banker_draws = banker_total <= 5
                            if banker_draws:  # Seul Banker tire
                                single_draw_count += 1
        
        total_combinations = 10**4 + 10**5  # Combinaisons avec et sans 3ème carte Player
        single_draw_prob = single_draw_count / total_combinations
        print(f"Combinaisons donnant tirage unique: {single_draw_count}")
        print(f"Probabilité théorique tirage unique: {single_draw_prob:.3f}")
        
        return single_draw_count, single_draw_prob
    
    def calculate_parties_by_burn_and_order(self, burn_cards):
        """Calcule les parties possibles pour un brûlage donné"""
        remaining_cards = self.total_cards - burn_cards
        burn_is_even = (burn_cards % 2 == 0)
        
        print(f"\n{'='*60}")
        print(f"CALCUL POUR BRÛLAGE {burn_cards} ({'PAIR' if burn_is_even else 'IMPAIR'})")
        print(f"Cartes restantes: {remaining_cards}")
        print("="*60)
        
        results = {
            'order_4_only': 0,
            'order_6_only': 0,
            'order_5_only': 0,
            'mixed_4_6': 0
        }
        
        if burn_is_even:
            # Parties paires possibles
            results.update(self.calculate_even_parties_exact(remaining_cards))
        else:
            # Parties impaires possibles
            results.update(self.calculate_odd_parties_exact(remaining_cards))
        
        return results
    
    def calculate_even_parties_exact(self, remaining_cards):
        """Calcule exactement les parties paires possibles"""
        results = {
            'order_4_only': 0,
            'order_6_only': 0,
            'mixed_4_6': 0
        }
        
        # ORDRE 4 UNIQUEMENT
        print("ORDRE 4 UNIQUEMENT (que des naturels):")
        if remaining_cards % 4 == 0:
            hands_needed = remaining_cards // 4
            print(f"  Mains nécessaires: {hands_needed}")
            
            # Vérifier si c'est réalisable avec les contraintes de naturels
            if self.is_all_naturals_possible(hands_needed, remaining_cards):
                results['order_4_only'] = 1
                print(f"  Résultat: 1 partie possible")
            else:
                print(f"  Résultat: IMPOSSIBLE (contraintes de naturels)")
        else:
            print(f"  Résultat: IMPOSSIBLE (division non exacte)")
        
        # ORDRE 6 UNIQUEMENT
        print("\nORDRE 6 UNIQUEMENT (que des doubles tirages):")
        if remaining_cards % 6 == 0:
            hands_needed = remaining_cards // 6
            print(f"  Mains nécessaires: {hands_needed}")
            
            # Vérifier si c'est réalisable avec les contraintes de double tirage
            if self.is_all_double_draws_possible(hands_needed, remaining_cards):
                results['order_6_only'] = 1
                print(f"  Résultat: 1 partie possible")
            else:
                print(f"  Résultat: IMPOSSIBLE (contraintes de double tirage)")
        else:
            print(f"  Résultat: IMPOSSIBLE (division non exacte)")
        
        # MÉLANGES 4+6
        print("\nMÉLANGES 4+6 (parties mixtes paires):")
        mixed_count = self.count_realistic_mixed_parties(remaining_cards)
        results['mixed_4_6'] = mixed_count
        print(f"  Résultat: {mixed_count} parties mixtes possibles")
        
        return results
    
    def calculate_odd_parties_exact(self, remaining_cards):
        """Calcule exactement les parties impaires possibles"""
        results = {
            'order_5_only': 0
        }
        
        # ORDRE 5 UNIQUEMENT
        print("ORDRE 5 UNIQUEMENT (que des tirages uniques):")
        if remaining_cards % 5 == 0:
            hands_needed = remaining_cards // 5
            print(f"  Mains nécessaires: {hands_needed}")
            
            # Vérifier si c'est réalisable avec les contraintes de tirage unique
            if self.is_all_single_draws_possible(hands_needed, remaining_cards):
                results['order_5_only'] = 1
                print(f"  Résultat: 1 partie possible")
            else:
                print(f"  Résultat: IMPOSSIBLE (contraintes de tirage unique)")
        else:
            print(f"  Résultat: IMPOSSIBLE (division non exacte)")
        
        return results
    
    def is_all_naturals_possible(self, hands_needed, remaining_cards):
        """Vérifie s'il est possible d'avoir QUE des naturels"""
        # Contraintes réalistes basées sur la composition du deck
        # Il faut suffisamment de cartes 8 et 9 pour forcer tous les naturels
        
        # Cartes donnant naturel: 8, 9 et leurs combinaisons
        # Estimation conservative: maximum 20 naturels consécutifs réalistes
        return hands_needed <= 20
    
    def is_all_double_draws_possible(self, hands_needed, remaining_cards):
        """Vérifie s'il est possible d'avoir QUE des doubles tirages"""
        # Contraintes très restrictives du tableau de tirage
        # Estimation conservative: maximum 15 doubles tirages consécutifs
        return hands_needed <= 15
    
    def is_all_single_draws_possible(self, hands_needed, remaining_cards):
        """Vérifie s'il est possible d'avoir QUE des tirages uniques"""
        # Plus probable que les doubles tirages mais toujours contraint
        # Estimation conservative: maximum 30 tirages uniques consécutifs
        return hands_needed <= 30
    
    def count_realistic_mixed_parties(self, remaining_cards):
        """Compte les parties mixtes réalistes"""
        count = 0
        
        # Énumérer les combinaisons réalistes
        max_hands_4 = min(remaining_cards // 4, 25)  # Limite réaliste
        
        for hands_4 in range(1, max_hands_4):
            cards_after_4 = remaining_cards - (hands_4 * 4)
            max_hands_6 = min(cards_after_4 // 6, 20)  # Limite réaliste
            
            for hands_6 in range(1, max_hands_6):
                total_hands = hands_4 + hands_6
                cards_used = (hands_4 * 4) + (hands_6 * 6)
                cards_unused = remaining_cards - cards_used
                
                # Vérifier si la combinaison est réaliste
                if (total_hands <= 40 and  # Limite totale de mains
                    cards_unused >= 0 and cards_unused <= 10 and  # Peu de cartes non utilisées
                    self.is_mixed_combination_realistic(hands_4, hands_6)):
                    count += 1
        
        return count
    
    def is_mixed_combination_realistic(self, hands_4, hands_6):
        """Vérifie si une combinaison mixte est réaliste"""
        # Vérifier l'équilibre entre naturels et doubles tirages
        # Plus il y a de naturels requis, moins c'est probable
        
        if hands_4 > 15:  # Trop de naturels requis
            return False
        if hands_6 > 12:  # Trop de doubles tirages requis
            return False
        
        return True
    
    def run_complete_analysis(self):
        """Exécute l'analyse complète par ordre"""
        
        print("="*70)
        print("CALCULATEUR EXACT DES PARTIES PAR ORDRE")
        print("Contraintes: Règles officielles + 312 cartes + brûlage")
        print("="*70)
        
        # Analyser les contraintes de base
        self.analyze_naturals_constraints()
        self.analyze_double_draw_constraints()
        self.analyze_single_draw_constraints()
        
        # Analyser chaque configuration de brûlage
        all_results = {}
        totals = {
            'order_4_total': 0,
            'order_6_total': 0,
            'order_5_total': 0,
            'mixed_4_6_total': 0
        }
        
        for burn_cards in range(2, 12):
            result = self.calculate_parties_by_burn_and_order(burn_cards)
            all_results[burn_cards] = result
            
            # Additionner les totaux
            totals['order_4_total'] += result.get('order_4_only', 0)
            totals['order_6_total'] += result.get('order_6_only', 0)
            totals['order_5_total'] += result.get('order_5_only', 0)
            totals['mixed_4_6_total'] += result.get('mixed_4_6', 0)
        
        print("\n" + "="*70)
        print("RÉSULTATS FINAUX PAR ORDRE")
        print("="*70)
        print(f"1. Parties d'ORDRE 4 (que des mains de 4 cartes): {totals['order_4_total']}")
        print(f"2. Parties d'ORDRE 6 (que des mains de 6 cartes): {totals['order_6_total']}")
        print(f"3. Parties d'ORDRE 5 (que des mains de 5 cartes): {totals['order_5_total']}")
        print(f"4. Parties MIXTES 4+6 (purement paires): {totals['mixed_4_6_total']}")
        
        total_pure_even = totals['order_4_total'] + totals['order_6_total'] + totals['mixed_4_6_total']
        total_all = total_pure_even + totals['order_5_total']
        
        print(f"\nTOTAL parties purement paires: {total_pure_even}")
        print(f"TOTAL parties purement impaires: {totals['order_5_total']}")
        print(f"TOTAL GÉNÉRAL: {total_all}")
        
        return {
            'totals': totals,
            'details': all_results
        }

if __name__ == "__main__":
    calculator = BaccaratExactPartiesByOrder()
    final_results = calculator.run_complete_analysis()
