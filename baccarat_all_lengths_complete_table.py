#!/usr/bin/env python3
"""
Tableau complet avec TOUTES les longueurs de 1 au maximum
Format optimisé pour affichage de toutes les probabilités
"""

import math

class BaccaratAllLengthsCompleteTable:
    def __init__(self):
        self.total_cards = 312
        
        # Probabilités officielles basées sur les règles du Baccarat (8 decks)
        self.hand_probabilities = {
            'natural_4_cards': 0.458597,      # Naturels + aucune 3ème carte
            'single_draw_5_cards': 0.446247,  # Un seul joueur tire
            'double_draw_6_cards': 0.095156,  # Les deux joueurs tirent
            'mixed_4_6_avg': 0.276876         # Moyenne pour séquences mixtes
        }
        
        # Longueurs maximales par brûlage
        self.max_lengths = {
            2: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            3: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            4: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 62},
            5: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            6: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            7: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            8: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            9: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0},
            10: {'order_4': 32, 'order_6': 40, 'order_5': 0, 'mixed_4_6': 61},
            11: {'order_4': 0, 'order_6': 0, 'order_5': 60, 'mixed_4_6': 0}
        }
        
        print("Tableau complet avec TOUTES les longueurs")
        print("De 1 jusqu'au maximum pour chaque type")
    
    def calculate_probability(self, base_prob, length):
        """Calcule la probabilité d'une séquence de longueur donnée"""
        if length <= 0:
            return 0
        return base_prob ** length
    
    def format_probability(self, prob):
        """Formate une probabilité pour affichage"""
        if prob == 0:
            return "-"
        elif prob >= 1e-2:
            return f"{prob:.4f}"
        elif prob >= 1e-6:
            return f"{prob:.2e}"
        else:
            return f"{prob:.1e}"
    
    def generate_order_4_complete_table(self):
        """Génère le tableau complet pour Ordre 4 (naturels)"""
        print("\n" + "="*100)
        print("ORDRE 4 (NATURELS) - TOUTES LES LONGUEURS")
        print("="*100)
        print("Probabilité de base: 0.458597 (45.86%)")
        print("-"*100)
        
        print(f"{'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12}")
        print("-"*100)
        
        base_prob = self.hand_probabilities['natural_4_cards']
        max_length = 32  # Maximum pour ordre 4
        
        # Afficher en 4 colonnes
        for i in range(0, max_length, 4):
            row = ""
            for j in range(4):
                length = i + j + 1
                if length <= max_length:
                    prob = self.calculate_probability(base_prob, length)
                    prob_str = self.format_probability(prob)
                    row += f"{length:>4} | {prob_str:>12} | "
                else:
                    row += f"{'':>4} | {'':>12} | "
            print(row.rstrip(" | "))
    
    def generate_order_6_complete_table(self):
        """Génère le tableau complet pour Ordre 6 (doubles tirages)"""
        print("\n" + "="*100)
        print("ORDRE 6 (DOUBLES TIRAGES) - TOUTES LES LONGUEURS")
        print("="*100)
        print("Probabilité de base: 0.095156 (9.52%)")
        print("-"*100)
        
        print(f"{'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12}")
        print("-"*100)
        
        base_prob = self.hand_probabilities['double_draw_6_cards']
        max_length = 40  # Maximum pour ordre 6
        
        # Afficher en 4 colonnes
        for i in range(0, max_length, 4):
            row = ""
            for j in range(4):
                length = i + j + 1
                if length <= max_length:
                    prob = self.calculate_probability(base_prob, length)
                    prob_str = self.format_probability(prob)
                    row += f"{length:>4} | {prob_str:>12} | "
                else:
                    row += f"{'':>4} | {'':>12} | "
            print(row.rstrip(" | "))
    
    def generate_order_5_complete_table(self):
        """Génère le tableau complet pour Ordre 5 (tirages uniques)"""
        print("\n" + "="*100)
        print("ORDRE 5 (TIRAGES UNIQUES) - TOUTES LES LONGUEURS")
        print("="*100)
        print("Probabilité de base: 0.446247 (44.62%)")
        print("-"*100)
        
        print(f"{'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12}")
        print("-"*100)
        
        base_prob = self.hand_probabilities['single_draw_5_cards']
        max_length = 60  # Maximum pour ordre 5
        
        # Afficher en 4 colonnes
        for i in range(0, max_length, 4):
            row = ""
            for j in range(4):
                length = i + j + 1
                if length <= max_length:
                    prob = self.calculate_probability(base_prob, length)
                    prob_str = self.format_probability(prob)
                    row += f"{length:>4} | {prob_str:>12} | "
                else:
                    row += f"{'':>4} | {'':>12} | "
            print(row.rstrip(" | "))
    
    def generate_mixed_complete_table(self):
        """Génère le tableau complet pour Mixte 4+6"""
        print("\n" + "="*100)
        print("MIXTE 4+6 (SÉQUENCES COMBINÉES) - TOUTES LES LONGUEURS")
        print("="*100)
        print("Probabilité de base: 0.276876 (27.69%)")
        print("-"*100)
        
        print(f"{'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12} | {'Long.':>4} | {'Probabilité':>12}")
        print("-"*100)
        
        base_prob = self.hand_probabilities['mixed_4_6_avg']
        max_length = 62  # Maximum pour mixte
        
        # Afficher en 4 colonnes
        for i in range(0, max_length, 4):
            row = ""
            for j in range(4):
                length = i + j + 1
                if length <= max_length:
                    prob = self.calculate_probability(base_prob, length)
                    prob_str = self.format_probability(prob)
                    row += f"{length:>4} | {prob_str:>12} | "
                else:
                    row += f"{'':>4} | {'':>12} | "
            print(row.rstrip(" | "))
    
    def generate_summary_by_burn(self):
        """Génère un résumé par brûlage"""
        print("\n" + "="*100)
        print("RÉSUMÉ PAR BRÛLAGE - LONGUEURS MAXIMALES DISPONIBLES")
        print("="*100)
        
        print(f"{'Brûlage':>8} | {'Cartes':>6} | {'Ordre 4':>8} | {'Ordre 6':>8} | {'Ordre 5':>8} | {'Mixte 4+6':>10}")
        print("-"*60)
        
        for burn_cards in range(2, 12):
            remaining = 312 - burn_cards
            max_data = self.max_lengths[burn_cards]
            burn_is_even = (burn_cards % 2 == 0)
            
            order_4 = max_data['order_4'] if max_data['order_4'] > 0 else "-"
            order_6 = max_data['order_6'] if max_data['order_6'] > 0 else "-"
            order_5 = max_data['order_5'] if max_data['order_5'] > 0 else "-"
            mixed = max_data['mixed_4_6'] if max_data['mixed_4_6'] > 0 else "-"
            
            print(f"{burn_cards:>8} | {remaining:>6} | {order_4:>8} | {order_6:>8} | {order_5:>8} | {mixed:>10}")
    
    def generate_practical_thresholds(self):
        """Génère les seuils pratiques pour chaque type"""
        print("\n" + "="*100)
        print("SEUILS PRATIQUES - LONGUEURS MAXIMALES PAR NIVEAU DE PROBABILITÉ")
        print("="*100)
        
        thresholds = [
            ("Très Probable", 0.1, "10%"),
            ("Probable", 0.01, "1%"),
            ("Possible", 0.001, "0.1%"),
            ("Rare", 1e-6, "0.0001%"),
            ("Très Rare", 1e-9, "1e-9"),
            ("Quasi Impossible", 1e-12, "1e-12")
        ]
        
        print(f"{'Seuil':>18} | {'Ordre 4':>8} | {'Ordre 6':>8} | {'Ordre 5':>8} | {'Mixte 4+6':>10}")
        print("-"*65)
        
        for name, threshold, percent in thresholds:
            # Calculer longueur max pour chaque type
            max_4 = int(math.log(threshold) / math.log(self.hand_probabilities['natural_4_cards'])) if threshold > 0 else 0
            max_6 = int(math.log(threshold) / math.log(self.hand_probabilities['double_draw_6_cards'])) if threshold > 0 else 0
            max_5 = int(math.log(threshold) / math.log(self.hand_probabilities['single_draw_5_cards'])) if threshold > 0 else 0
            max_mix = int(math.log(threshold) / math.log(self.hand_probabilities['mixed_4_6_avg'])) if threshold > 0 else 0
            
            print(f"{name:>18} | {max_4:>8} | {max_6:>8} | {max_5:>8} | {max_mix:>10}")
    
    def run_complete_analysis(self):
        """Exécute l'analyse complète avec toutes les longueurs"""
        
        print("="*100)
        print("TABLEAU COMPLET AVEC TOUTES LES LONGUEURS")
        print("Probabilités pour chaque longueur de 1 au maximum")
        print("="*100)
        
        # Générer tous les tableaux complets
        self.generate_order_4_complete_table()
        self.generate_order_6_complete_table()
        self.generate_order_5_complete_table()
        self.generate_mixed_complete_table()
        
        # Générer les résumés
        self.generate_summary_by_burn()
        self.generate_practical_thresholds()
        
        print("\n" + "="*100)
        print("NOTES IMPORTANTES")
        print("="*100)
        print("1. Toutes les probabilités sont identiques pour tous les brûlages du même type")
        print("2. Seules les longueurs maximales changent selon les cartes restantes")
        print("3. Les probabilités diminuent exponentiellement avec la longueur")
        print("4. Les longueurs > 10 sont très rares en pratique")
        print("5. Les longueurs maximales sont purement théoriques")

if __name__ == "__main__":
    calculator = BaccaratAllLengthsCompleteTable()
    calculator.run_complete_analysis()
