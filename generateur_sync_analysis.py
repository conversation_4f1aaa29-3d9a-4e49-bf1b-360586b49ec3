#!/usr/bin/env python3
"""
Générateur et analyseur de parties baccarat avec synchronisation forcée.

Ce programme génère des parties de baccarat avec états de synchronisation
contrôlés et analyse l'impact sur les relations Same/Opposite.
"""

import random
import csv
import json
from typing import List, Dict, Tuple
from collections import defaultdict
import argparse
from datetime import datetime


class SimulateurBaccaratSync:
    """
    Simulateur baccarat avec contrôle de synchronisation.
    """

    def __init__(self, mode_sync: str = "normal"):
        """
        Initialise le simulateur.

        Args:
            mode_sync: "normal", "force_sync", "force_desync"
        """
        self.mode_sync = mode_sync
        self.deck_complet = []
        self.position_carte_coupe = 0
        self.position_actuelle = 0
        self.cartes_brulees = 0
        self.etat_synchronise = True

    def creer_deck_controle(self) -> List[int]:
        """Crée un deck selon le mode de synchronisation."""
        if self.mode_sync == "force_sync":
            return self.creer_deck_force_sync()
        elif self.mode_sync == "force_desync":
            return self.creer_deck_force_desync()
        else:
            return self.creer_deck_normal()

    def creer_deck_normal(self) -> List[int]:
        """Crée un deck normal (8 decks mélangés)."""
        deck = []
        for _ in range(8):
            for valeur in [1,2,3,4,5,6,7,8,9,0,0,0,0]:
                for _ in range(4):
                    deck.append(valeur)
        random.shuffle(deck)
        return deck

    def creer_deck_force_sync(self) -> List[int]:
        """Crée un deck favorisant la synchronisation (4 ou 6 cartes)."""
        deck = []

        # 60% de naturels (8-9) pour forcer 4 cartes
        for _ in range(int(416 * 0.3)):
            deck.extend([8, 9])

        # 40% de cartes faibles (0-5) pour forcer 6 cartes (les deux tirent)
        for _ in range(int(416 * 0.35)):
            deck.extend([0, 1, 2, 3, 4, 5])

        # Compléter avec cartes moyennes
        while len(deck) < 416:
            deck.append(random.randint(0, 9))

        random.shuffle(deck)
        return deck[:416]

    def creer_deck_force_desync(self) -> List[int]:
        """Crée un deck favorisant la désynchronisation (5 cartes)."""
        deck = []

        # Alternance pour forcer qu'un seul tire
        # Player tire (0-5), Banker reste (6-7)
        for i in range(208):
            if i % 2 == 0:  # Positions Player
                deck.append(random.choice([0,1,2,3,4,5]))  # Player tire
            else:  # Positions Banker
                deck.append(random.choice([6,7]))  # Banker reste

        # Compléter
        while len(deck) < 416:
            deck.append(random.randint(0, 9))

        random.shuffle(deck)
        return deck[:416]

    def placer_carte_coupe(self):
        """Place la carte de coupe vers 75% du sabot."""
        self.position_carte_coupe = int(len(self.deck_complet) * 0.75)

    def bruler_cartes(self):
        """Brûle 6-8 cartes au début."""
        nb_cartes_brulees = random.randint(6, 8)
        self.cartes_brulees = nb_cartes_brulees
        self.position_actuelle = nb_cartes_brulees

        # État initial selon brûlage
        self.etat_synchronise = (nb_cartes_brulees % 2 == 0)

    def tirer_carte(self) -> int:
        """Tire la prochaine carte du sabot."""
        if self.position_actuelle >= self.position_carte_coupe:
            raise Exception("Carte de coupe atteinte")

        carte = self.deck_complet[self.position_actuelle]
        self.position_actuelle += 1
        return carte

    def calculer_total(self, cartes: List[int]) -> int:
        """Calcule le total baccarat (modulo 10)."""
        return sum(cartes) % 10

    def player_doit_tirer(self, total_player: int) -> bool:
        """Règles Player : 0-5 tire, 6-7 reste, 8-9 naturel."""
        return total_player in [0,1,2,3,4,5]

    def banker_doit_tirer(self, total_banker: int, carte_player_3: int = None) -> bool:
        """Règles Banker conditionnelles."""
        if total_banker in [8,9]:
            return False

        if carte_player_3 is None:
            return total_banker in [0,1,2,3,4,5]

        if total_banker in [0,1,2]:
            return True
        elif total_banker == 3:
            return carte_player_3 != 8
        elif total_banker == 4:
            return carte_player_3 in [2,3,4,5,6,7]
        elif total_banker == 5:
            return carte_player_3 in [4,5,6,7]
        elif total_banker == 6:
            return carte_player_3 in [6,7]
        else:
            return False

    def simuler_manche(self, index_manche: int) -> Dict:
        """Simule une manche complète."""
        position_debut = self.position_actuelle
        etat_sync_debut = self.etat_synchronise

        # Distribution initiale P-B-P-B
        player_cartes = [self.tirer_carte(), self.tirer_carte()]
        banker_cartes = [self.tirer_carte(), self.tirer_carte()]

        total_player = self.calculer_total(player_cartes)
        total_banker = self.calculer_total(banker_cartes)

        # Gestion 3ème carte
        if not (total_player in [8,9] or total_banker in [8,9]):
            carte_player_3 = None

            if self.player_doit_tirer(total_player):
                carte_player_3 = self.tirer_carte()
                player_cartes.append(carte_player_3)
                total_player = self.calculer_total(player_cartes)

            if self.banker_doit_tirer(total_banker, carte_player_3):
                banker_cartes.append(self.tirer_carte())
                total_banker = self.calculer_total(banker_cartes)

        # Calcul synchronisation
        cartes_utilisees = len(player_cartes) + len(banker_cartes)
        if cartes_utilisees == 5:
            self.etat_synchronise = not self.etat_synchronise

        # Résultat
        if total_player > total_banker:
            resultat = 'P'
        elif total_banker > total_player:
            resultat = 'B'
        else:
            resultat = 'T'

        return {
            'index_manche': index_manche,
            'resultat': resultat,
            'cartes_utilisees': cartes_utilisees,
            'etat_sync_debut': etat_sync_debut,
            'etat_sync_fin': self.etat_synchronise
        }

    def simuler_partie_complete(self) -> Dict:
        """Simule une partie complète."""
        self.deck_complet = self.creer_deck_controle()
        self.placer_carte_coupe()
        self.bruler_cartes()

        manches = []
        index_manche = 1

        try:
            while index_manche <= 60:  # Minimum 60 manches indexées
                manche = self.simuler_manche(index_manche)
                manches.append(manche)

                # Incrémenter index seulement si P ou B
                if manche['resultat'] in ['P', 'B']:
                    index_manche += 1

        except Exception:
            pass

        return {
            'manches': manches,
            'cartes_brulees': self.cartes_brulees,
            'etat_initial_sync': (self.cartes_brulees % 2 == 0),
            'mode_simulation': self.mode_sync
        }


class AnalyseurResultats:
    """
    Analyseur des résultats de synchronisation.
    """

    def __init__(self):
        self.stats_globales = defaultdict(int)
        self.stats_par_mode = defaultdict(lambda: defaultdict(int))
        self.sequences_so = []

    def convertir_so(self, resultats_pb: List[str]) -> List[str]:
        """Convertit une séquence P/B en S/O."""
        if len(resultats_pb) < 2:
            return []

        sequence_so = []
        for i in range(1, len(resultats_pb)):
            if resultats_pb[i-1] == resultats_pb[i]:
                sequence_so.append('S')
            else:
                sequence_so.append('O')

        return sequence_so

    def analyser_partie(self, partie: Dict) -> Dict:
        """Analyse une partie complète."""
        manches = partie['manches']

        # Extraire résultats P/B seulement
        resultats_pb = [m['resultat'] for m in manches if m['resultat'] in ['P', 'B']]

        # Convertir en S/O
        sequence_so = self.convertir_so(resultats_pb)

        # Analyser par état de synchronisation
        stats_sync = {'S': 0, 'O': 0}
        stats_desync = {'S': 0, 'O': 0}

        for i, manche in enumerate(manches):
            if manche['resultat'] in ['P', 'B'] and i > 0:
                # Trouver l'index dans sequence_so
                so_index = len([m for m in manches[:i+1] if m['resultat'] in ['P', 'B']]) - 2

                if so_index >= 0 and so_index < len(sequence_so):
                    so_result = sequence_so[so_index]

                    if manche['etat_sync_debut']:
                        stats_sync[so_result] += 1
                    else:
                        stats_desync[so_result] += 1

        return {
            'resultats_pb': resultats_pb,
            'sequence_so': sequence_so,
            'stats_sync': stats_sync,
            'stats_desync': stats_desync,
            'nb_manches_pb': len(resultats_pb),
            'nb_conversions_so': len(sequence_so)
        }

    def analyser_parties_multiples(self, parties: List[Dict]) -> Dict:
        """Analyse multiple parties."""
        stats_aggregees = {
            'total_parties': len(parties),
            'total_manches_pb': 0,
            'total_conversions_so': 0,
            'stats_sync_globales': {'S': 0, 'O': 0},
            'stats_desync_globales': {'S': 0, 'O': 0},
            'par_mode': defaultdict(lambda: {
                'parties': 0,
                'stats_sync': {'S': 0, 'O': 0},
                'stats_desync': {'S': 0, 'O': 0}
            })
        }

        for partie in parties:
            analyse = self.analyser_partie(partie)
            mode = partie['mode_simulation']

            # Agréger statistiques
            stats_aggregees['total_manches_pb'] += analyse['nb_manches_pb']
            stats_aggregees['total_conversions_so'] += analyse['nb_conversions_so']

            # Stats globales
            for so_type in ['S', 'O']:
                stats_aggregees['stats_sync_globales'][so_type] += analyse['stats_sync'][so_type]
                stats_aggregees['stats_desync_globales'][so_type] += analyse['stats_desync'][so_type]

            # Stats par mode
            stats_aggregees['par_mode'][mode]['parties'] += 1
            for so_type in ['S', 'O']:
                stats_aggregees['par_mode'][mode]['stats_sync'][so_type] += analyse['stats_sync'][so_type]
                stats_aggregees['par_mode'][mode]['stats_desync'][so_type] += analyse['stats_desync'][so_type]

        # Calculer pourcentages
        self.calculer_pourcentages(stats_aggregees)

        return stats_aggregees

    def calculer_pourcentages(self, stats: Dict):
        """Calcule les pourcentages pour les statistiques."""
        # Pourcentages globaux
        total_sync = sum(stats['stats_sync_globales'].values())
        total_desync = sum(stats['stats_desync_globales'].values())

        if total_sync > 0:
            stats['pourcentages_sync_globaux'] = {
                'S': stats['stats_sync_globales']['S'] / total_sync * 100,
                'O': stats['stats_sync_globales']['O'] / total_sync * 100
            }

        if total_desync > 0:
            stats['pourcentages_desync_globaux'] = {
                'S': stats['stats_desync_globales']['S'] / total_desync * 100,
                'O': stats['stats_desync_globales']['O'] / total_desync * 100
            }

        # Pourcentages par mode
        for mode, data in stats['par_mode'].items():
            total_sync_mode = sum(data['stats_sync'].values())
            total_desync_mode = sum(data['stats_desync'].values())

            if total_sync_mode > 0:
                data['pourcentages_sync'] = {
                    'S': data['stats_sync']['S'] / total_sync_mode * 100,
                    'O': data['stats_sync']['O'] / total_sync_mode * 100
                }

            if total_desync_mode > 0:
                data['pourcentages_desync'] = {
                    'S': data['stats_desync']['S'] / total_desync_mode * 100,
                    'O': data['stats_desync']['O'] / total_desync_mode * 100
                }


class GenerateurAnalyseMassive:
    """
    Générateur et analyseur massif de parties.
    """

    def __init__(self):
        self.simulateur = None
        self.analyseur = AnalyseurResultats()

    def generer_parties(self, nb_parties: int, mode_sync: str) -> List[Dict]:
        """Génère un nombre donné de parties."""
        print(f"🎯 Génération de {nb_parties} parties en mode {mode_sync}...")

        parties = []
        for i in range(nb_parties):
            self.simulateur = SimulateurBaccaratSync(mode_sync)
            partie = self.simulateur.simuler_partie_complete()
            parties.append(partie)

            if (i + 1) % (nb_parties // 10) == 0:
                print(f"   Progression: {i + 1}/{nb_parties} ({(i+1)/nb_parties*100:.1f}%)")

        print(f"✅ {nb_parties} parties générées en mode {mode_sync}")
        return parties

    def analyser_et_rapporter(self, parties: List[Dict], mode_sync: str) -> Dict:
        """Analyse les parties et génère un rapport."""
        print(f"📊 Analyse des résultats pour mode {mode_sync}...")

        analyse = self.analyseur.analyser_parties_multiples(parties)

        # Générer rapport
        rapport = self.generer_rapport(analyse, mode_sync)

        return {
            'analyse_complete': analyse,
            'rapport': rapport,
            'parties_brutes': parties
        }

    def generer_rapport(self, analyse: Dict, mode_sync: str) -> str:
        """Génère un rapport textuel des résultats."""
        rapport = []
        rapport.append(f"\n{'='*60}")
        rapport.append(f"RAPPORT D'ANALYSE - MODE {mode_sync.upper()}")
        rapport.append(f"{'='*60}")

        # Statistiques générales
        rapport.append(f"\n📊 STATISTIQUES GÉNÉRALES:")
        rapport.append(f"   Parties analysées: {analyse['total_parties']:,}")
        rapport.append(f"   Manches P/B totales: {analyse['total_manches_pb']:,}")
        rapport.append(f"   Conversions S/O totales: {analyse['total_conversions_so']:,}")

        # Résultats globaux
        if 'pourcentages_sync_globaux' in analyse:
            rapport.append(f"\n🔄 RÉSULTATS EN MODE SYNCHRONISÉ:")
            rapport.append(f"   Same (S): {analyse['pourcentages_sync_globaux']['S']:.2f}%")
            rapport.append(f"   Opposite (O): {analyse['pourcentages_sync_globaux']['O']:.2f}%")

            ecart_sync = analyse['pourcentages_sync_globaux']['S'] - 50.0
            rapport.append(f"   Écart vs 50%: {ecart_sync:+.2f}%")

        if 'pourcentages_desync_globaux' in analyse:
            rapport.append(f"\n⚡ RÉSULTATS EN MODE DÉSYNCHRONISÉ:")
            rapport.append(f"   Same (S): {analyse['pourcentages_desync_globaux']['S']:.2f}%")
            rapport.append(f"   Opposite (O): {analyse['pourcentages_desync_globaux']['O']:.2f}%")

            ecart_desync = analyse['pourcentages_desync_globaux']['S'] - 50.0
            rapport.append(f"   Écart vs 50%: {ecart_desync:+.2f}%")

        # Comparaison si les deux modes existent
        if 'pourcentages_sync_globaux' in analyse and 'pourcentages_desync_globaux' in analyse:
            diff_s = (analyse['pourcentages_sync_globaux']['S'] -
                     analyse['pourcentages_desync_globaux']['S'])
            rapport.append(f"\n🎯 DIFFÉRENCE SYNC vs DÉSYNC:")
            rapport.append(f"   Différence Same (S): {diff_s:+.2f}%")
            rapport.append(f"   Impact de la synchronisation: {'FAVORABLE à S' if diff_s > 0 else 'FAVORABLE à O'}")

        return "\n".join(rapport)

    def sauvegarder_resultats(self, resultats: Dict, nom_fichier: str):
        """Sauvegarde les résultats dans un fichier."""
        # Sauvegarder analyse JSON
        with open(f"{nom_fichier}_analyse.json", 'w', encoding='utf-8') as f:
            # Exclure les parties brutes pour éviter des fichiers trop volumineux
            analyse_light = {
                'analyse_complete': resultats['analyse_complete'],
                'rapport': resultats['rapport'],
                'timestamp': datetime.now().isoformat()
            }
            json.dump(analyse_light, f, indent=2, ensure_ascii=False)

        # Sauvegarder rapport texte
        with open(f"{nom_fichier}_rapport.txt", 'w', encoding='utf-8') as f:
            f.write(resultats['rapport'])

        # Sauvegarder données CSV pour analyse externe
        self.sauvegarder_csv(resultats['parties_brutes'], f"{nom_fichier}_donnees.csv")

        print(f"📁 Résultats sauvegardés:")
        print(f"   - {nom_fichier}_analyse.json")
        print(f"   - {nom_fichier}_rapport.txt")
        print(f"   - {nom_fichier}_donnees.csv")

    def sauvegarder_csv(self, parties: List[Dict], nom_fichier: str):
        """Sauvegarde les parties au format CSV."""
        with open(nom_fichier, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # En-têtes
            writer.writerow([
                'partie_id', 'cartes_brulees', 'etat_initial_sync',
                'sequence_complete', 'sequence_sync', 'sequence_so',
                'nb_manches_totales', 'nb_manches_indexees', 'mode_simulation'
            ])

            # Données
            for i, partie in enumerate(parties):
                analyse = self.analyseur.analyser_partie(partie)

                # Construire séquences
                sequence_complete = ','.join([m['resultat'] for m in partie['manches']])
                sequence_sync = ','.join(['true' if m['etat_sync_debut'] else 'false'
                                        for m in partie['manches']])
                sequence_so = ','.join(analyse['sequence_so'])

                writer.writerow([
                    i + 1,
                    partie['cartes_brulees'],
                    partie['etat_initial_sync'],
                    sequence_complete,
                    sequence_sync,
                    sequence_so,
                    len(partie['manches']),
                    analyse['nb_manches_pb'],
                    partie['mode_simulation']
                ])


def main():
    """Fonction principale du programme."""
    parser = argparse.ArgumentParser(
        description="Générateur et analyseur de parties baccarat avec synchronisation"
    )

    parser.add_argument(
        '--nb-parties',
        type=int,
        choices=[1000, 10000, 100000],
        default=1000,
        help="Nombre de parties à générer (1000, 10000, ou 100000)"
    )

    parser.add_argument(
        '--mode',
        choices=['normal', 'force_sync', 'force_desync', 'all'],
        default='all',
        help="Mode de synchronisation"
    )

    parser.add_argument(
        '--output',
        type=str,
        default='baccarat_analysis',
        help="Préfixe des fichiers de sortie"
    )

    args = parser.parse_args()

    print(f"🎯 GÉNÉRATEUR BACCARAT SYNCHRONISATION")
    print(f"📊 Configuration:")
    print(f"   Nombre de parties: {args.nb_parties:,}")
    print(f"   Mode: {args.mode}")
    print(f"   Fichiers de sortie: {args.output}_*")
    print(f"   Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    generateur = GenerateurAnalyseMassive()

    # Déterminer les modes à exécuter
    if args.mode == 'all':
        modes = ['normal', 'force_sync', 'force_desync']
    else:
        modes = [args.mode]

    resultats_globaux = {}

    # Exécuter pour chaque mode
    for mode in modes:
        print(f"\n{'='*60}")
        print(f"TRAITEMENT MODE: {mode.upper()}")
        print(f"{'='*60}")

        # Générer parties
        parties = generateur.generer_parties(args.nb_parties, mode)

        # Analyser et rapporter
        resultats = generateur.analyser_et_rapporter(parties, mode)

        # Afficher rapport
        print(resultats['rapport'])

        # Sauvegarder
        nom_fichier = f"{args.output}_{mode}_{args.nb_parties}"
        generateur.sauvegarder_resultats(resultats, nom_fichier)

        resultats_globaux[mode] = resultats

    # Rapport comparatif si plusieurs modes
    if len(modes) > 1:
        print(f"\n{'='*60}")
        print("RAPPORT COMPARATIF")
        print(f"{'='*60}")

        generer_rapport_comparatif(resultats_globaux, args.nb_parties)

    print(f"\n🎉 ANALYSE TERMINÉE!")
    print(f"📁 Tous les fichiers sauvegardés avec préfixe: {args.output}_")


def generer_rapport_comparatif(resultats: Dict, nb_parties: int):
    """Génère un rapport comparatif entre les modes."""
    print(f"\n📊 COMPARAISON DES {nb_parties:,} PARTIES PAR MODE:")

    for mode, data in resultats.items():
        analyse = data['analyse_complete']

        print(f"\n🎯 MODE {mode.upper()}:")

        if 'pourcentages_sync_globaux' in analyse:
            s_sync = analyse['pourcentages_sync_globaux']['S']
            print(f"   Sync → Same: {s_sync:.2f}% (écart: {s_sync-50:+.2f}%)")

        if 'pourcentages_desync_globaux' in analyse:
            s_desync = analyse['pourcentages_desync_globaux']['S']
            print(f"   Désync → Same: {s_desync:.2f}% (écart: {s_desync-50:+.2f}%)")

    # Calcul des écarts maximaux
    if 'force_sync' in resultats and 'force_desync' in resultats:
        sync_s = resultats['force_sync']['analyse_complete']['pourcentages_sync_globaux']['S']
        desync_s = resultats['force_desync']['analyse_complete']['pourcentages_desync_globaux']['S']

        ecart_total = sync_s - desync_s

        print(f"\n🚀 ÉCART MAXIMAL DÉTECTÉ:")
        print(f"   Force Sync (S): {sync_s:.2f}%")
        print(f"   Force Désync (S): {desync_s:.2f}%")
        print(f"   DIFFÉRENCE: {ecart_total:.2f}%")

        if ecart_total > 10:
            print(f"   🎯 BIAIS EXPLOITABLE CONFIRMÉ!")
        else:
            print(f"   ⚠️ Biais modéré détecté")


if __name__ == "__main__":
    main()
